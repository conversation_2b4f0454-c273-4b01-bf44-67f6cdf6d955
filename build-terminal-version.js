const fs = require('fs')
const path = require('path')
const fsExtra = require('fs-extra')
const { name, version, smallVersion } = require('./package.json')

let packVersion
const arch = process.arch

const initVersion = () => {
	let date = new Date()
	let year = date.getFullYear()
	let month = String(date.getMonth() + 1).padStart(2, '0')
	let day = String(date.getDate()).padStart(2, '0')
	let hours = String(date.getHours()).padStart(2, '0')
	let min = String(date.getMinutes()).padStart(2, '0')
	packVersion = version + '.' + (smallVersion ? smallVersion + '.' : '') + year + '.' + month + '.' + day + '.' + hours + '' + min
}

const wirtePackageVersion = () => {
	let pkgPath = path.join(__dirname, 'package.json')
	let pjson = fs.readFileSync(pkgPath)
	pjson = JSON.parse(pjson)
	pjson.build.appId = packVersion.replace(/\./g, '')
	pjson.actualVersion = packVersion
	if (arch !== 'ia32') {
		const packPath = pjson.build.directories.output || '.'
		const outputPathMap = {
			x64: `${packPath}/linux-unpacked`,
			arm64: `${packPath}/linux-arm64-unpacked`
		}
		pjson.scripts.mkdeb = `cd ./x64_linux && sudo chmod +x ./*.sh && sed -i 's/\r$//' ./*.rules ./*.sh ./scripts/* && ./mkdeb.sh ${packVersion} ${pjson.name} ${arch == 'x64' ? 'amd64' : 'arm64'} ${outputPathMap[arch]}`
	} else {
		pjson.build.win.artifactName = `${name}_${arch}_${packVersion}.exe`
	}
	fs.writeFileSync(pkgPath, JSON.stringify(pjson, null, 2))
}

const updateConfigFile = () => {
	const filePath = path.join(__dirname, '/extraResources/serverConfig.json')
	const config = JSON.parse(fs.readFileSync(filePath) || '{}') || {}
	if (arch == 'arm64') {
		config.lightCom = '/dev/ttyS3'
		config.lockCom = '/dev/ttyS3'
	} else if (arch == 'x64') {
		config.lightCom = '/dev/ttyS2'
		config.lockCom = '/dev/ttyS2'
	} else {
		config.lightCom = 'COM3'
		config.lockCom = 'COM3'
	}
	config.iotAddress = ''
	config.productName = ''
	fs.writeFileSync(filePath, JSON.stringify(config, null, 4))
}

const delFaceSdk = () => {
	const resourcesDir = path.join(process.cwd(), 'extraResources/face_sdk')
	const joinSourcesPath = (pathStr = '') => path.join(resourcesDir, pathStr)
	const delSourcesDir = (arr) => {
		arr.forEach((item) => {
			item = joinSourcesPath(item)
			fsExtra
				.remove(item)
				.then(() => {
					console.log(item + '：目录已成功删除')
				})
				.catch((err) => {
					console.error(item + '：删除目录时出错', err)
				})
		})
	}
	if (arch == 'ia32') {
		delSourcesDir(['amd64', 'arm64'])
	} else if (arch == 'x64') {
		delSourcesDir(['arm64', 'win32'])
	} else if (arch == 'arm64') {
		delSourcesDir(['amd64', 'win32'])
	}
}

initVersion()
wirtePackageVersion()
updateConfigFile()
delFaceSdk()
