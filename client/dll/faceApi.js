const path = require('path')
const ffi = require('ffi-napi')
const ref = require('ref-napi')
const StructType = require('ref-struct-di')(ref)
const ArrayType = require('ref-array-di')(ref)
// const axios = require('axios')
const http = require('http');
const PNG = require('pngjs').PNG;

const { ipcMain, app } = require('electron')
const Logger = require('../log/index')
const log = new Logger('face')

class FaceApi {
	constructor() {
		this.params = {
			resourcesPath: '',
			photoshopMode: ''
		}
		this.extraResources = null
		this.faceMethods = null
		this.faceId = null
		/**
		 * 人脸检测参数信息
		 * format：图像格式, RGB888 = 0 , BGR888 = 1 , RGBX8888 = 2 , BGRX8888 = 3 , YUV420P = 4 , I420 = YUV420P , NV12 = 5 , NV21 = 6
		 */
		this._SD_IMAGE = StructType({
			format: ref.types.int,
			planeDatas: ArrayType(ref.refType(ref.types.uchar), 4),
			planeStrides: ArrayType(ref.types.int, 4),
			width: ref.types.int,
			height: ref.types.int
		})
		// 实际人脸区域信息
		this._FACE_INFO = StructType({
			x: ref.types.int,
			y: ref.types.int,
			width: ref.types.int,
			height: ref.types.int,
			faceId: ref.types.int
		})
	}

	async urlToBGRNative(imageUrl) {
		console.log(imageUrl,'imageUrlimageUrlimageUrl')
		return new Promise((resolve, reject) => {
			http.get(imageUrl, (response) => {
				response
					.pipe(new PNG())
					.on('parsed', function () {
						const bgrData = new Uint8Array(this.width * this.height * 3)

						for (let y = 0; y < this.height; y++) {
							for (let x = 0; x < this.width; x++) {
								const idx = (this.width * y + x) * 3
								const srcIdx = (this.width * y + x) << 2

								bgrData[idx] = this.data[srcIdx + 2] // B
								bgrData[idx + 1] = this.data[srcIdx + 1] // G
								bgrData[idx + 2] = this.data[srcIdx] // R
							}
						}

						resolve({
							data: bgrData,
							width: this.width,
							height: this.height,
							channels: 3
						})
					})
					.on('error', reject)
			})
		})
	}
	processImage() {
		console.log('processImageprocessImageprocessImageprocessImage')
		// 使用示例
		;(async () => {
			try {
				const bgr = await this.urlToBGRNative('http://*************:9010/acp/54f456e2fe70483c9655f066beaa429a.jpg')
				console.log(`成功转换图像: ${bgr.width}x${bgr.height}`)
				log.info(`成功转换图像: ${bgr.width}x${bgr.height}`)
				console.log('前10个像素的BGR值:', bgr.data.slice(0, 30))
				log.info('前10个像素的BGR值:', bgr.data.slice(0, 30))
			} catch (error) {
				console.error('处理失败:', error)
			}
		})()
	}

	/**
	 * 初始化人脸检测方法
	 */
	_initFaceMethods() {
		const detectorID = ref.refType(ref.types.int32)
		// 定义基本类型
		const int = ref.types.int
		const float = ref.types.float
		// 定义 long long 类型
		const int64 = ref.types.longlong
		const int64Ptr = ref.refType(int64)
		const uchar = ref.types.uchar
		const charPtr = ref.refType(ref.types.char)
		const ucharPtr = ref.refType(uchar)
		const signedCharPtr = ref.refType(ref.types.char) // signed char*
		const signedCharPtrPtr = ref.refType(signedCharPtr) // signed char**
		const faceInfo = ref.refType(this._FACE_INFO)

		// 关键点数量常量（SDK定义FR_POINT_NUM=5）
		const FR_POINT_NUM = 5

		// 二维浮点坐标 - 根据头文件定义修正字段名
		const GoPoint2f = StructType({
			fX: ref.types.float, // 修正：使用fX而不是x
			fY: ref.types.float  // 修正：使用fY而不是y
		})
		const FacePointsArray = ArrayType(GoPoint2f, FR_POINT_NUM)

		// 人脸姿态结构体 - 根据头文件定义
		const FacePose = StructType({
			fHorizontal_threshold: ref.types.float // 人脸水平偏转程度限制
		})

		// 人脸检测参数 - 根据头文件FaceDetectFilterParams定义修正
		const FaceDetectFilterParams = StructType({
			nX: ref.types.int,          // 人脸检测区域左上角横坐标
			nY: ref.types.int,          // 人脸检测区域左上角纵坐标
			nWidth: ref.types.int,      // 人脸检测区域宽度
			nHeight: ref.types.int,     // 人脸检测区域高度
			nMin_size: ref.types.int,   // 最小人脸
			nMax_size: ref.types.int,   // 最大人脸
			pose_thresold: FacePose,    // 姿态估计阈值
			nStream_type: ref.types.int // 流类型：0表示视频流，1表示图片流
		})

		// 人脸区域结构体 - 根据头文件FaceTrackedRect定义修正
		const FaceTrackedRect = StructType({
			nX: ref.types.int,                    // 人脸区域左上角横坐标
			nY: ref.types.int,                    // 人脸区域左上角纵坐标
			nWidth: ref.types.int,                // 人脸区域宽度
			nHeight: ref.types.int,               // 人脸区域高度
			nID: ref.types.int,                   // 人脸跟踪ID
			face_points: FacePointsArray          // 人脸特征点集
		})

		// 结构体指针类型
		const FaceTrackedRectPtr = ref.refType(FaceTrackedRect)
		const FaceTrackedRectPtrPtr = ref.refType(FaceTrackedRectPtr)
		const FaceDetectFilterParamsPtr = ref.refType(FaceDetectFilterParams)

		// 定义512字节特征数组类型
		const FEATURE_SIZE = 512
		const FaceFeatureArray = ArrayType(ref.types.char, FEATURE_SIZE) // signed char

		// 人员信息结构体 - 根据头文件PersonInfo定义
		const PersonInfo = StructType({
			person_id_: ref.types.int,      // 人员ID,唯一
			face_feature_: FaceFeatureArray // 人脸特征 - 修正字段名
		})

		// 检索结果结构体 - 根据头文件PersonSearchInfo定义
		const PersonSearchInfo = StructType({
			person_id_: ref.types.int,   // 人员ID,唯一
			similarity_: ref.types.float // 相似度
		})

		// 指针和数组类型定义
		const PersonSearchInfoPtr = ref.refType(PersonSearchInfo)
		const PersonSearchInfoPtrPtr = ref.refType(PersonSearchInfoPtr)
		const PersonInfoPtr = ref.refType(PersonInfo)
		const intPtr = ref.refType(ref.types.int)
		const floatPtr = ref.refType(ref.types.float)


		log.info('=== 开始初始化人脸方法 ===')
		log.info(`操作系统平台: ${process.platform}`)
		log.info(`CPU架构: ${process.arch}`)
		log.info(`extraResources路径: ${this.extraResources}`)
		log.info(`resourcesPath: ${this.params.resourcesPath}`)

		if (process.platform == 'win32') {
			const pathToAdd = path.join(this.extraResources, this.params.resourcesPath.substring(0, this.params.resourcesPath.lastIndexOf('/')))
			process.env.PATH += `${path.delimiter}${pathToAdd}`
			log.info(`Windows平台，已添加到PATH: ${pathToAdd}`)
		}

		const libPath = path.join(this.extraResources, this.params.resourcesPath)
		log.info(`动态库完整路径: ${libPath}`)

		// 检查库文件是否存在
		const fs = require('fs')
		if (fs.existsSync(libPath)) {
			log.info('动态库文件存在，开始加载')
		} else {
			log.error(`动态库文件不存在: ${libPath}`)
			throw new Error(`动态库文件不存在: ${libPath}`)
		}

		console.log(libPath, 'libPathlibPathlibPath')

		try {
			log.info('开始创建 FFI Library...')
			this.faceMethods = new ffi.Library(libPath, {
			// 得到最近一次的错误码
			GoNc_FaceRecognition_GetLastErrorCode: ['int', []],

			// SDK全局初始化 - 修正参数类型
			GoNc_FaceRecognition_Init: ['int', [charPtr]], // const char* license_file

			// SDK全局反初始化
			GoNc_FaceRecognition_CleanUp: ['int', []],

			// 创建人脸识别实例 - 修正参数类型
			GoNc_FaceRecognition_Create: ['int', [charPtr, int64Ptr]], // const char* szModel_path, long long *phHandle

			// 释放人脸识别实例
			GoNc_FaceRecognition_Release: ['int', [int64]], // const long long hHandle

			// YUV-NV21转换为BGR图像
			GoNc_FaceRecognition_Nv21Tbgr: ['int', [int64, ucharPtr, ucharPtr, int, int]],

			// 人脸检测 - 根据头文件修正参数类型
			GoNc_FaceRecognition_FaceDetect: [
				'int', // 返回值
				[
					int64,                          // const long long hHandle
					ucharPtr,                       // const unsigned char* szImg_buf
					int,                            // int nWidth
					int,                            // int nHeight
					FaceDetectFilterParamsPtr,      // const FaceDetectFilterParams* pDet_filter_params
					int,                            // int nNum_threads
					FaceTrackedRectPtrPtr,          // FaceTrackedRect** ppFace_rc_arr
					intPtr                          // int* pnFace_count
				]
			],

			// 单帧活体检测
			GoNc_FaceRecognition_AntiSpoofingProcess: [
				'int',
				[
					int64,              // long long hHandle
					ucharPtr,           // const unsigned char* szImg_buf
					int,                // int nWidth
					int,                // int nHeight
					FaceTrackedRectPtr, // FaceTrackedRect* FacesRC
					int,                // int nNum_threads
					intPtr              // int* asResult
				]
			],

			// 双目单帧活体检测
			GoNc_FaceRecognition_AntiSpoofingProcess_DoubleSensor: [
				'int',
				[
					int64,                     // long long hHandle
					ucharPtr,                  // const unsigned char* szImg_buf
					int,                       // int nWidth
					int,                       // int nHeight
					FaceTrackedRectPtr,        // FaceTrackedRect* FacesRC
					ucharPtr,                  // const unsigned char* szImg_buf_ir
					int,                       // int nWidth_ir
					int,                       // int nHeight_ir
					FaceDetectFilterParamsPtr, // const FaceDetectFilterParams* pDet_filter_params_ir
					int,                       // int nNum_threads
					intPtr                     // int* asResult
				]
			],

			// 人脸特征提取 - 修正参数类型
			GoNc_FaceRecognition_GetFaceFeature: [
				'int', // 返回值
				[
					int64,                 // const long long hHandle
					ucharPtr,              // const unsigned char* szImg_buf
					int,                   // int nWidth
					int,                   // int nHeight
					FaceTrackedRectPtr,    // const FaceTrackedRect* pFace_rc
					int,                   // int nNum_threads
					signedCharPtrPtr,      // signed char** ppFeature
					intPtr                 // int* pnFeature_len
				]
			],

			// 人脸特征比对
			GoNc_FaceRecognition_CompareFeature: [
				'int',
				[
					int64,        // const long long hHandle
					charPtr,      // const signed char* szFeature1
					intPtr,       // const int* npFeature_len1
					charPtr,      // const signed char* szFeature2
					intPtr,       // const int* npFeature_len2
					floatPtr      // float* fpSimilarity
				]
			],

			// 1:N检索 - 修正参数类型
			GoNc_FaceRecognition_SearchPerson: [
				'int', // 返回值
				[
					int64,                    // long long hHandle
					charPtr,                  // const signed char* feature_
					int,                      // int feature_len_
					float,                    // float thresold_
					int,                      // int max_num_
					PersonSearchInfoPtrPtr,   // PersonSearchInfo **searched_result_
					intPtr                    // int *result_num_
				]
			],

			// 人脸遮挡检测
			GoNc_FaceRecognition_OcclusionFace: [
				'int',
				[
					int64,              // long long hHandle
					ucharPtr,           // const unsigned char* szImg_buf
					int,                // int nWidth
					int,                // int nHeight
					FaceTrackedRect,    // FaceTrackedRect FacesRC
					int,                // int nNum_threads
					intPtr              // GoNcFaceOcclusion* occlusionResult
				]
			],

			// 人员管理函数
			GoNc_FaceRecognition_ImportPersons: ['int', [int64, PersonInfoPtr, int]], // long long hHandle, PersonInfo *persons_, int person_num_
			GoNc_FaceRecognition_ClearPersons: ['void', [int64]], // long long hHandle
			GoNc_FaceRecognition_AddPerson: ['int', [int64, PersonInfo, float]], // long long hHandle, PersonInfo person_, float rep_thresold_
			GoNc_FaceRecognition_DeletePerson: ['int', [int64, int]], // long long hHandle, int person_id_
			GoNc_FaceRecognition_UpdatePersonFeature: ['int', [int64, PersonInfo]], // long long hHandle, PersonInfo person_

			// 活体检测相关函数
			GoNc_FaceRecognition_AntiSpoofing_SetFaceActionType: ['int', [int64, int]], // long long hHandle, GoNcFaceAction type
			GoNc_FaceRecognition_AntiSpoofing_PushFrame: ['int', [int64, ucharPtr, int, int, int64]], // long long hHandle, const unsigned char* szImg_buf, int nWidth, int nHeight, long long timestamp
			GoNc_FaceRecognition_AntiSpoofing_SetCB: ['int', [int64, 'pointer']] // long long hHandle, GoNc_AntiSpoofingCB cb
		})
		log.info('FFI Library 创建成功')
		log.info('已定义的函数数量:', Object.keys(this.faceMethods).length)
		log.info('=== 人脸方法初始化完成 ===')

		// 调用初始化
		this._initFace()
	} catch (error) {
		log.error('初始化人脸方法失败:', error.toString())
		throw error
	}
	}
	/**
	 * 初始化SDK
	 */
	_initFace() {
		log.info('开始初始化人脸SDK...')
		try {
			// 修正：GoNc_FaceRecognition_Init 接受 const char* license_file 参数
			const ret = this.faceMethods.GoNc_FaceRecognition_Init(null)
			console.log(this.params.resourcesPath, 'this.params.resourcesPath')
			log.info(ret, '人脸SDK初始化结果')
			if (ret === 0) {
				log.info('人脸SDK初始化成功')
			} else {
				log.error(`人脸SDK初始化失败，错误码: ${ret}`)
			}
			return ret
		} catch (error) {
			log.error('人脸SDK初始化异常:', error.toString())
			return -1
		}
	}
	/**
	 * 创建人脸检测器
	 */
	_createFaceRecognition(photoshopMode) {
		log.info('开始创建人脸检测器...')
		log.info(`photoshopMode: ${photoshopMode}`)

		try {
			// 修正：GoNc_FaceRecognition_Create 第二个参数是 long long *phHandle
			const outDetectorID = ref.alloc('longlong')
			log.info('已分配 outDetectorID 内存')

			// 修正：正确构建模型路径
			const modelPath = path.join(this.extraResources, 'face_sdk/amd64_new/models')
			log.info(`模型路径: ${modelPath}`)
			log.info(`extraResources: ${this.extraResources}`)

			// 检查模型路径是否存在
			const fs = require('fs')
			if (fs.existsSync(modelPath)) {
				log.info('模型路径存在，继续创建检测器')
			} else {
				log.error(`模型路径不存在: ${modelPath}`)
				return -1
			}

			log.info('调用 GoNc_FaceRecognition_Create...')
			const ret = this.faceMethods.GoNc_FaceRecognition_Create(modelPath, outDetectorID)
			log.info(`GoNc_FaceRecognition_Create 返回值: ${ret}`)

			if (ret === 0) {
				this.faceId = outDetectorID.deref()
				log.info(`创建人脸检测器成功，faceId: ${this.faceId}`)
			} else {
				this.faceId = null
				log.error(`创建人脸检测器失败，错误码: ${ret}`)
			}

			return ret
		} catch (error) {
			log.error('创建人脸检测器异常:', error.toString())
			this.faceId = null
			return -1
		}
	}
	/**
	 * 配置人脸检测器 - 注意：SDFACESDK_FinishConfig 在头文件中未定义，可能不需要此方法
	 */
	_configFaceRecognition() {
		// 该函数在 GoNcFaceRegSDK.h 中未定义，可能不需要配置步骤
		log.info(0, '配置人脸检测器结果 - 跳过配置步骤')
		return 0
	}
	/**
	 * 人脸检测 - 注意：需要重新实现，因为原函数SDFACESDK_DetectMaxFaceByBase64不存在
	 */
	_detectFaceRecognition(imageData) {
		if (this.faceId === null) this._createFaceRecognition(this.params.photoshopMode)

		// 注意：原来的 SDFACESDK_DetectMaxFaceByBase64 函数在头文件中不存在
		// 需要使用 GoNc_FaceRecognition_FaceDetect 函数重新实现
		// 这里暂时返回null，需要根据实际需求重新实现
		log.info('_detectFaceRecognition 方法需要重新实现，使用正确的SDK函数')
		return null

		// TODO: 使用以下函数重新实现：
		// 1. 将base64转换为BGR图像数据
		// 2. 调用 GoNc_FaceRecognition_FaceDetect 进行人脸检测
		// 3. 处理检测结果
	}
	/**
	 * 销毁检测器 && 反初始化SDK
	 */
	_destoryDetector() {
		log.info('开始销毁人脸检测器...')
		if (this.faceId != null) {
			try {
				log.info(`销毁检测器，faceId: ${this.faceId}`)
				const releaseRet = this.faceMethods.GoNc_FaceRecognition_Release(this.faceId)
				log.info(`GoNc_FaceRecognition_Release 返回值: ${releaseRet}`)

				const cleanupRet = this.faceMethods.GoNc_FaceRecognition_CleanUp()
				log.info(`GoNc_FaceRecognition_CleanUp 返回值: ${cleanupRet}`)

				this.faceId = null
				log.info('人脸检测器销毁完成')
			} catch (error) {
				log.error('销毁人脸检测器异常:', error.toString())
			}
		} else {
			log.info('faceId 为空，无需销毁')
		}
	}
	/**
	 * 初始化人脸检测器
	 */
	initDev(photoshopMode) {
		log.info('=== 开始初始化人脸检测设备 ===')
		log.info(`请求的 photoshopMode: ${photoshopMode}`)
		log.info(`当前的 photoshopMode: ${this.params.photoshopMode}`)
		log.info(`当前的 faceId: ${this.faceId}`)

		console.log(photoshopMode, this.params.photoshopMode && this.faceId)

		if (photoshopMode == this.params.photoshopMode && this.faceId != null) {
			log.info('检测器已初始化，无需重复初始化')
		} else {
			log.info('需要重新初始化检测器')

			if (this.faceId != null) {
				log.info('检测到已存在的检测器，先销毁')
				// 销毁原先的检测器
				this._destoryDetector()
			}

			log.info('步骤1: 初始化人脸SDK')
			if (this._initFace() != 0) {
				log.error('初始化人脸SDK失败，终止初始化流程')
				return false
			}

			log.info('步骤2: 创建人脸检测器')
			if (this._createFaceRecognition(photoshopMode) != 0) {
				log.error('创建人脸检测器失败，终止初始化流程')
				return false
			}

			this.params.photoshopMode = photoshopMode
			log.info(`已更新 photoshopMode 为: ${photoshopMode}`)

			log.info('步骤3: 配置人脸检测器')
			if (this._configFaceRecognition() != 0) {
				log.warn('配置人脸检测器失败，但继续执行')
			}

			log.info('=== 人脸检测设备初始化完成 ===')
		}
		return true
	}
	/**
	 * 销毁监听事件
	 */
	unInitDev() {
		this._destoryDetector()
		ipcMain.removeAllListeners('init_face_config')
		ipcMain.removeAllListeners('face_recognition_path')
		ipcMain.removeAllListeners('destory_face_detector')
	}
	/**
	 * 初始化, 当参数值更改时，先 unInitDev(), 再重新初始化
	 */
	init() {
		/**
		 * params.resourcesPath 资源文件 dll/so 路径（相对于项目下 extraResources 文件夹的路径）extraResources\face_sdk\amd64\models
		 * params.photoshopMode 算法模式
		 */

		ipcMain.on('init_face_config', (event, params) => {
			log.info('=== 收到 init_face_config 事件 ===')
			log.info('接收到的参数:', JSON.stringify(params))
			console.log(this.faceId, 'this.faceId')

			if (this.faceId == null) {
				log.info('当前 faceId 为空，销毁原先的检测器')
				// 销毁原先的检测器
				this._destoryDetector()
			}

			if (params.photoshopMode == -1) {
				log.error('人脸检测算法不能为空')
				return
			}

			const faceResourcesPathMap = {
				arm64: '/face_sdk/arm64/libSdFaceSDK.so',
				x64: '/face_sdk/amd64_new/libGoNcFaceRegSDK.so', // '/face_sdk/amd64/libSdFaceSDK.so',
				ia32: '/face_sdk/win32/SdFaceSDK.dll'
			}

			this.extraResources = path.join(app.isPackaged ? process.resourcesPath : process.cwd(), 'extraResources')
			log.info(`CPU架构: ${process.arch}`)
			log.info(`应用是否打包: ${app.isPackaged}`)
			log.info(`extraResources基础路径: ${this.extraResources}`)

			this.params.resourcesPath = faceResourcesPathMap[process.arch]
			log.info(`选择的资源路径: ${this.params.resourcesPath}`)

			if (!this.params.resourcesPath) {
				log.error(`不支持的CPU架构: ${process.arch}`)
				return
			}

			this.processImage()
			log.info(`完整资源文件路径: ${this.extraResources + this.params.resourcesPath}`)

			try {
				this._initFaceMethods()
				const initResult = this.initDev(params.photoshopMode)
				log.info(`设备初始化结果: ${initResult}`)
			} catch (error) {
				log.error('初始化过程中发生异常:', error.toString())
			}
		})
		/**
		 * params
		 * params.base64 base64图片
		 */
		ipcMain.on('face_recognition_path', (event, params) => {
			console.log( 'paramsparamsparams')
			if (!params.base64) {
				log.info('图片信息不能为空！', '人脸识别错误')
				event.returnValue = null
				return
			}
			try {
				const resultImagData = this._detectFaceRecognition(params.base64.replace(/^data:image\/\w+;base64,/, ''))
				if (resultImagData) {
					const { x, y, width, height } = resultImagData
					event.returnValue = {
						x,
						y,
						width,
						height
					}
				}
				event.returnValue = null
			} catch (e) {
				log.info(e, '人脸识别错误12')
				event.returnValue = null
			}
		})
		/**
		 * 销毁人脸检测容器
		 */
		ipcMain.on('destory_face_detector', () => {
			this._destoryDetector()
		})
	}
}

module.exports = FaceApi
