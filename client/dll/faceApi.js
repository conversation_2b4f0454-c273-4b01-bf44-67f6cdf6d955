const path = require('path')
const ffi = require('ffi-napi')
const ref = require('ref-napi')
const StructType = require('ref-struct-di')(ref)
const ArrayType = require('ref-array-di')(ref)
// const axios = require('axios')
const http = require('http');
const PNG = require('pngjs').PNG;

const { ipcMain, app } = require('electron')
const Logger = require('../log/index')
const log = new Logger('face')

class FaceApi {
	constructor() {
		this.params = {
			resourcesPath: '',
			photoshopMode: ''
		}
		this.extraResources = null
		this.faceMethods = null
		this.faceId = null
		/**
		 * 人脸检测参数信息
		 * format：图像格式, RGB888 = 0 , BGR888 = 1 , RGBX8888 = 2 , BGRX8888 = 3 , YUV420P = 4 , I420 = YUV420P , NV12 = 5 , NV21 = 6
		 */
		this._SD_IMAGE = StructType({
			format: ref.types.int,
			planeDatas: ArrayType(ref.refType(ref.types.uchar), 4),
			planeStrides: ArrayType(ref.types.int, 4),
			width: ref.types.int,
			height: ref.types.int
		})
		// 实际人脸区域信息
		this._FACE_INFO = StructType({
			x: ref.types.int,
			y: ref.types.int,
			width: ref.types.int,
			height: ref.types.int,
			faceId: ref.types.int
		})
	}

	async urlToBGRNative(imageUrl) {
		console.log(imageUrl,'imageUrlimageUrlimageUrl')
		return new Promise((resolve, reject) => {
			http.get(imageUrl, (response) => {
				response
					.pipe(new PNG())
					.on('parsed', function () {
						const bgrData = new Uint8Array(this.width * this.height * 3)

						for (let y = 0; y < this.height; y++) {
							for (let x = 0; x < this.width; x++) {
								const idx = (this.width * y + x) * 3
								const srcIdx = (this.width * y + x) << 2

								bgrData[idx] = this.data[srcIdx + 2] // B
								bgrData[idx + 1] = this.data[srcIdx + 1] // G
								bgrData[idx + 2] = this.data[srcIdx] // R
							}
						}

						resolve({
							data: bgrData,
							width: this.width,
							height: this.height,
							channels: 3
						})
					})
					.on('error', reject)
			})
		})
	}
	processImage() {
		console.log('processImageprocessImageprocessImageprocessImage')
		// 使用示例
		;(async () => {
			try {
				const bgr = await this.urlToBGRNative('http://192.168.3.251:9010/acp/54f456e2fe70483c9655f066beaa429a.jpg')
				console.log(`成功转换图像: ${bgr.width}x${bgr.height}`)
				log.info(`成功转换图像: ${bgr.width}x${bgr.height}`)
				console.log('前10个像素的BGR值:', bgr.data.slice(0, 30))
				log.info('前10个像素的BGR值:', bgr.data.slice(0, 30))
			} catch (error) {
				console.error('处理失败:', error)
			}
		})()
	}

	/**
	 * 初始化人脸检测方法
	 */
	_initFaceMethods() {
		const detectorID = ref.refType(ref.types.int32)
		// 定义基本类型
		const int = ref.types.int
		const float = ref.types.float
		// 定义 long long 类型
		const int64 = ref.types.longlong
		const int64Ptr = ref.refType(int64)
		const uchar = ref.types.uchar
		const charPtr = ref.refType(ref.types.char)
		const ucharPtr = ref.refType(uchar)
		const faceInfo = ref.refType(this._FACE_INFO)
		// 二维浮点坐标
		const GoPoint2f = StructType({
			x: ref.types.float,
			y: ref.types.float
		})

		// 关键点数量常量（假设SDK定义FR_POINT_NUM=5）
		const FR_POINT_NUM = 5
		const FacePointsArray = ArrayType(GoPoint2f, FR_POINT_NUM)

		const FaceTrackedRect = StructType({
			nX: ref.types.int, // 左上角X
			nY: ref.types.int, // 左上角Y
			nWidth: ref.types.int, // 宽度
			nHeight: ref.types.int, // 高度
			nID: ref.types.int, // 跟踪ID
			face_points: FacePointsArray // 5个关键点
		})
		// 结构体指针类型
		const FaceTrackedRectPtr = ref.refType(FaceTrackedRect)
		// 人脸检测参数
		const FaceDetectFilterParam = StructType({
			minFaceSize: int, // 最小人脸尺寸
			maxFaceSize: int, // 最大人脸尺寸
			threshold: ref.types.float, // 置信度阈值
			enableLiveness: int // 是否启用活体检测
			// 根据实际SDK定义补充其他参数
		})
		// 定义 FacePose 结构体
		const FacePose = StructType({
			fHorizontal_threshold: ref.types.float // 32位浮点数
		})
		// 结构体指针类型
		const FacePosePtr = ref.refType(FacePose)
		// 默认值常量
		const DEFAULT_POSE_THRESHOLD = 0.6
		// 定义512字节特征数组类型
		const FEATURE_SIZE = 512
		const FaceFeatureArray = ArrayType(ref.types.char, FEATURE_SIZE)

		// 人员信息结构体
		const PersonInfo = StructType({
			person_id_: ref.types.int,
			face_feature: FaceFeatureArray
		})
		// 检索结果结构体
		const PersonSearchInfo = StructType({
			person_id_: ref.types.int, // 人员ID
			similarity_: ref.types.float // 相似度 (0~1)
		})

		// 指针和数组类型定义
		const PersonSearchInfoPtr = ref.refType(PersonSearchInfo)
		const PersonSearchInfoArray = ArrayType(PersonSearchInfo)
		// 指针类型定义
		const PersonInfoPtr = ref.refType(PersonInfo)
		// 定义FR_POINTF结构体
		const FR_POINTF = StructType({
			x: ref.types.float, // X轴坐标 (32位浮点)
			y: ref.types.float // Y轴坐标 (32位浮点)
		})
		// 定义FR_RECT结构体
		const FR_RECT = StructType({
			x: ref.types.int, // 左上角X坐标
			y: ref.types.int, // 左上角Y坐标
			width: ref.types.int, // 矩形宽度
			height: ref.types.int // 矩形高度
		})
		// 定义FR_RECT2F结构体（假设成员实际为float类型）
		const FR_RECT2F = StructType({
			x: ref.types.float, // 左上角X坐标
			y: ref.types.float, // 左上角Y坐标
			width: ref.types.float, // 矩形宽度
			height: ref.types.float // 矩形高度
		})

		// 定义FR_FACE_POSE结构体（假设定义）
		const FR_FACE_POSE = StructType({
			yaw: ref.types.float, // 偏航角阈值
			pitch: ref.types.float, // 俯仰角阈值
			roll: ref.types.float // 旋转角阈值
		})

		// 定义FR_DETE_PARAM主结构体
		const FR_DETE_PARAM = StructType({
			x: ref.types.int, // ROI区域X坐标
			y: ref.types.int, // ROI区域Y坐标
			width: ref.types.int, // ROI宽度
			height: ref.types.int, // ROI高度
			minFace: ref.types.int, // 最小人脸像素
			maxFace: ref.types.int, // 最大人脸像素
			pose_threshold: FR_FACE_POSE, // 姿态阈值结构体
			AmbiguityThreshold: ref.types.int, // 质量阈值(默认60)
			streamType: ref.types.int // 流类型(0:视频流,1:图片流)
		})
		const FR_POINTF_ARRAY = ArrayType(FR_POINTF, FR_POINT_NUM)

		// 主结构体定义
		const FR_TRACK_RECT = StructType({
			x: ref.types.int, // 左上角X
			y: ref.types.int, // 左上角Y
			width: ref.types.int, // 宽度
			height: ref.types.int, // 高度
			ID: ref.types.int, // 跟踪ID
			faceFeaturePoints: FR_POINTF_ARRAY // 5个关键点
		})

		if (process.platform == 'win32') {
			process.env.PATH += `${path.delimiter}${path.join(this.extraResources, this.params.resourcesPath.substring(0, this.params.resourcesPath.lastIndexOf('/')))}`
		}
		const libPath = path.join(this.extraResources, this.params.resourcesPath)
		console.log(libPath, 'libPathlibPathlibPath')
		this.faceMethods = new ffi.Library(libPath, {
			// SDK全局初始化
			GoNc_FaceRecognition_Init: ['int', ['string']],
			// 创建人脸检测器
			GoNc_FaceRecognition_Create: ['int', ['string', int64Ptr]],
			// 人脸特征提取
			GoNc_FaceRecognition_GetFaceFeature: [
				'int', // 返回值
				[
					int64, // hHandle
					ucharPtr, // szImg_buf
					int, // nWidth
					int, // nHeight
					ref.refType(FaceTrackedRect), // pFace_rc
					int, // nNum_threads
					charPtr, // ppFeature (signed char**)
					ref.refType(int) // pnFeature_len (int*)
				]
			],
			// 新增人员
			GoNc_FaceRecognition_AddPerson: ['int', ['string', int64Ptr]],
			// 配置检测器
			SDFACESDK_FinishConfig: ['int', ['int']],
			// 人脸检测
			GoNc_FaceRecognition_FaceDetect: [
				'int', // 返回值
				[
					int64, // hHandle
					ucharPtr, // szImg_buf  //bgr格式的图像
					int, // nWidth
					int, // nHeight
					ref.refType(FaceDetectFilterParam), // pDet_filter_params
					int, // nNum_threads
					ref.refType(ref.refType(FaceTrackedRect)), // ppFace_rc_arr (FaceTrackedRect**)
					ref.refType(int) // pnFace_count (int*)
				]
			],
			// 假设需要手动释放内存的函数
			GoNc_FaceRecognition_FreeDetectionResults: ['void', ['pointer', int]],
			GoNc_FaceRecognition_SearchPerson: [
				int, // 返回值
				[int64, charPtr, int, float, int, 'pointer', 'pointer'] // 参数
			],
			GoNc_FaceRecognition_FreeSearchResults: [
				'void',
				['pointer', int] // 释放内存
			],
			// 销毁检测器
			GoNc_FaceRecognition_Release: ['int', [int64]],
			// SDK全局反初始化
			GoNc_FaceRecognition_CleanUp: ['int', []]
		})
		log.info('初始指针方法完成', this.faceMethods)
		this._initFace()
	}
	/**
	 * 初始化SDK
	 */
	_initFace() {
		const ret = this.faceMethods.GoNc_FaceRecognition_Init(null, true)
		console.log(this.params.resourcesPath, 'this.params.resourcesPath')
		log.info(ret, '人脸初始化结果')
		return ret
	}
	/**
	 * 创建人脸检测器
	 */
	_createFaceRecognition(photoshopMode) {
		const outDetectorID = ref.alloc('int')
		const ret = this.faceMethods.GoNc_FaceRecognition_Create(this.params.resourcesPath + '/face_sdk/amd64_new/models', Number(photoshopMode))
		log.info(ret, '创建人脸检测器结果')
		this.faceId = ret == 0 ? outDetectorID.deref() : null
		return ret
	}
	/**
	 * 配置人脸检测器
	 */
	_configFaceRecognition() {
		const ret = this.faceMethods.SDFACESDK_FinishConfig(this.faceId)
		log.info(ret, '配置人脸检测器结果')
		return ret
	}
	/**
	 * 人脸检测
	 */
	_detectFaceRecognition(imageData) {
		if (this.faceId === null) this._createFaceRecognition(this.params.photoshopMode)
		// 初始化一个指向 this._FACE_INFO 类的指针, 一个引用对象
		const faceImagData = ref.alloc(this._FACE_INFO)
		try {
			const ret = this.faceMethods.SDFACESDK_DetectMaxFaceByBase64(this.faceId, imageData, imageData.length, '', faceImagData)
			log.info(ret, '人脸识别结果')
			if (ret === 0) {
				// deref() 获取指针实例对应的值
				return faceImagData.deref()
			} else {
				return null
			}
		} catch (e) {
			log.info(e.toString(), '人脸识别错误')
			return null
		}
	}
	/**
	 * 销毁检测器 && 反初始化SDK
	 */
	_destoryDetector() {
		if (this.faceId != null) {
			this.faceMethods.GoNc_FaceRecognition_Release(this.faceId)
			this.faceMethods.GoNc_FaceRecognition_CleanUp()
		}
	}
	/**
	 * 初始化人脸检测器
	 */
	initDev(photoshopMode) {
		console.log(photoshopMode, this.params.photoshopMode && this.faceId)
		if (photoshopMode == this.params.photoshopMode && this.faceId != null) {
			log.info('已初始化了')
		} else {
			log.info('人脸id：', this.faceId)
			if (this.faceId != null) {
				// 销毁原先的检测器
				this._destoryDetector()
			}
			if (this._initFace() != 0) {
				log.info('初始化人脸检测器失败')
				return
			}
			if (this._createFaceRecognition(photoshopMode) != 0) {
				log.info('创建人脸检测器失败')
				return
			}
			this.params.photoshopMode = photoshopMode
			if (this._configFaceRecognition() != 0) {
				log.info('配置人脸检测器失败')
			}
		}
	}
	/**
	 * 销毁监听事件
	 */
	unInitDev() {
		this._destoryDetector()
		ipcMain.removeAllListeners('init_face_config')
		ipcMain.removeAllListeners('face_recognition_path')
		ipcMain.removeAllListeners('destory_face_detector')
	}
	/**
	 * 初始化, 当参数值更改时，先 unInitDev(), 再重新初始化
	 */
	init() {
		/**
		 * params.resourcesPath 资源文件 dll/so 路径（相对于项目下 extraResources 文件夹的路径）extraResources\face_sdk\amd64\models
		 * params.photoshopMode 算法模式
		 */

		ipcMain.on('init_face_config', (event, params) => {
			console.log(this.faceId, 'this.faceId')
			if (this.faceId == null) {
				// 销毁原先的检测器
				this._destoryDetector()
			}
			if (params.photoshopMode == -1) {
				log.info('人脸检测算法不能为空')
				return
			}
			const faceResourcesPathMap = {
				arm64: '/face_sdk/arm64/libSdFaceSDK.so',
				x64: '/face_sdk/amd64_new/libGoNcFaceRegSDK.so', // '/face_sdk/amd64/libSdFaceSDK.so',
				ia32: '/face_sdk/win32/SdFaceSDK.dll'
			}
			this.extraResources = path.join(app.isPackaged ? process.resourcesPath : process.cwd(), 'extraResources')
			log.info(process.arch, 'process.arch')
			this.params.resourcesPath = faceResourcesPathMap[process.arch] //
			this.processImage()
			log.info('资源文件路径', this.extraResources + this.params.resourcesPath)
			this._initFaceMethods()
			this.initDev(params.photoshopMode)
		})
		/**
		 * params
		 * params.base64 base64图片
		 */
		ipcMain.on('face_recognition_path', (event, params) => {
			console.log( 'paramsparamsparams')
			if (!params.base64) {
				log.info('图片信息不能为空！', '人脸识别错误')
				event.returnValue = null
				return
			}
			try {
				const resultImagData = this._detectFaceRecognition(params.base64.replace(/^data:image\/\w+;base64,/, ''))
				if (resultImagData) {
					const { x, y, width, height } = resultImagData
					event.returnValue = {
						x,
						y,
						width,
						height
					}
				}
				event.returnValue = null
			} catch (e) {
				log.info(e, '人脸识别错误12')
				event.returnValue = null
			}
		})
		/**
		 * 销毁人脸检测容器
		 */
		ipcMain.on('destory_face_detector', () => {
			this._destoryDetector()
		})
	}
}

module.exports = FaceApi
