const path = require('path')
const { ipcMain, app } = require('electron')
const ffi = require('ffi-napi')

let oskdll = ''
if (app.isPackaged) {
	oskdll = path.join(process.resourcesPath, '/extraResources/osk', 'CallOsk.dll')
} else {
	oskdll = path.join(process.cwd(), '/extraResources/osk', 'CallOsk.dll')
}
let oskInstance = null
const oskApi = {
	initDll() {
		oskInstance = new ffi.Library(oskdll, {
			// 文件内的方法和参数类型
			CallOsk: ['int', []]
		})
		ipcMain.on('osk-open-dll', (event, arg) => {
			const result = oskInstance.CallOsk()
			event.reply('reply-osk-open', { result: result })
		})
	}
}
module.exports = oskApi
