const FaceApi = require('./faceApi')
const oskApi = require('./oskApi') // 系统软键盘
const faceApi = new FaceApi()
let isunInit = true
function initDll(initArray) {
	// 确保已经反初始化后才可调用初始化
	if (!isunInit) return
	if (initArray.includes('face')) faceApi.init()
	if (initArray.includes('osk') && process.platform === 'win32') oskApi.initDll()
	isunInit = false
}
function unitDll(unitArray) {
	isunInit = true
	if (unitArray.includes('face')) faceApi.unInitDev()
}
module.exports = { initDll, unitDll }
