const { app } = require('electron')
const electronLog = require('electron-log')
const fs = require('fs')
const path = require('path')
const logParentDir = 'Log'

/**
 * 日志类（默认放入main文件夹）
 * 传入前缀pathSuffix代表新创建一个文件夹存放日志文件, 创建一个新electronLog实例，
 * 否则不创建新的electronLog实例
 */
class Log {
    constructor(pathSuffix = '') {
        this.logger = pathSuffix ? electronLog.create('anotherInstance') : electronLog

        const logPath = path.join(Log.getPath(pathSuffix || 'main'), Log.getLogFileName())

        this.logger.transports.file.level = 'debug'
        this.logger.transports.file.maxSize = 1002430 // 10M
        this.logger.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} {text}'
        this.logger.transports.file.resolvePath = () => logPath
    }

    // 日志文件目录
    static getPath(pathSuffix) {
        const basePath = app.isPackaged ? path.dirname(app.getPath('exe')) : process.cwd()
        let logPath = path.join(basePath, logParentDir)
        if (!fs.existsSync(logPath)) {
            fs.mkdirSync(logPath)
        }
        if (pathSuffix) {
            logPath = path.join(logPath, pathSuffix)
            if (!fs.existsSync(logPath)) {
                fs.mkdirSync(logPath)
            }
        }
        return logPath
    }

    // 日志文件名称
    static getLogFileName() {
        let date = new Date()
        date = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate()
        return date + '.log'
    }

    // 删除日志文件
    deleteLog(logSaveDay) {
        try {
            const basePath = app.isPackaged ? path.dirname(app.getPath('exe')) : process.cwd()
            const logPath = path.join(basePath, logParentDir)
            const dirArr = fs.readdirSync(logPath) || []
            dirArr.forEach((item) => {
                const fullpath = path.join(logPath, item)
                // const stats = fs.statSync(fullpath)
                const childDirArr = fs.readdirSync(fullpath)
                // const logDay = (new Date().getTime() - stats.birthtime.getTime()) / 1000 / 60 / 60 / 24
                childDirArr.forEach((child) => {
                    const fullpath2 = path.join(fullpath, child)
                    const stats = fs.statSync(fullpath2)
                    const logDay = (new Date().getTime() - stats.birthtime.getTime()) / 1000 / 60 / 60 / 24
                    if (logDay > logSaveDay) {
                        // 删除文件
                        if (stats.isDirectory()) {
                            fs.rmdirSync(fullpath, { recursive: true })
                        } else {
                            fs.unlink(fullpath, (error) => {
                                if (error) {
                                    this.error(error, '删除日志文件出错')
                                }
                            })
                        }
                    }
                })
            })
        } catch (e) {
            this.error(e, '删除日志文件异常')
        }
    }

    info() {
        this.logger.info.apply(this, arguments)
    }

    warn() {
        this.logger.warn.apply(this, arguments)
    }

    error() {
        this.logger.error.apply(this, arguments)
    }

    debug() {
        this.logger.debug.apply(this, arguments)
    }

    verbose() {
        this.logger.verbose.apply(this, arguments)
    }

    silly() {
        this.logger.silly.apply(this, arguments)
    }
}

module.exports = Log
