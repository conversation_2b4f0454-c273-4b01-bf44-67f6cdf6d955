const { ipcMain } = require('electron')
const { exec } = require('child_process')
const Logger = require('../log/index')
const tools = require('../mqtt/tools')
const Updater = require('../upgrad/index')
const log = new Logger('mqtt-ipc')

// cmd执行命令
const _exec = (command) => {
    exec(command, (err, stdout, stderr) => {
        if (err) {
            log.info(`执行exec命令 ${command} 出错`, err)
            return
        }
        log.info(`exec命令${command} stdout`, stdout)
        log.info(`exec命令${command} stderr`, stderr)
    })
}

module.exports = {
    init(mainWindow) {
        // 升级事件监听
        const updater = new Updater(mainWindow)
        updater.on('download-progress', (downloadInfo) => {
            log.info('下载安装包中, 进度：', downloadInfo.progress)
            mainWindow.webContents.send('upgrade-progress', downloadInfo)
        })
        updater.on('error', (err) => {
            log.info('升级失败：', err)
            global.upgradeStatus = false
            mainWindow.webContents.send('upgrade-status', false)
        })
        // 重启终端
        ipcMain.on('reboot-terminal', () => {
            tools.rebootTerminal()
        })
        // 重启设备
        ipcMain.on('reboot-device', () => {
            tools.reboot()
        })
        // 属性上报
        ipcMain.on('property-post', async(event, props) => {
            const properties = await tools.getProperty(props)
            mainWindow.webContents.send('property-post', properties)
        })
        // 应用升级
        ipcMain.on('upgrage', (e, downloadInfo) => {
            log.info('应用升级, 下载相关参数: ', downloadInfo)
            global.upgradeStatus = true
            updater.download(downloadInfo)
        })
        // 修改系统时间
        ipcMain.on('ntp', (event, dateTime) => {
            try {
                log.info('修改系统时间: ', dateTime)
                if (process.platform === 'win32') {
                    const dateTimeArr = dateTime.split(' ')
                    log.info('时间拆分: ', dateTimeArr)
                    const date = dateTimeArr[0]
                    const time = dateTimeArr[1]
                    _exec(`date ${date} && time ${time}`)
                } else {
                    _exec(`sudo date -s "${dateTime}"`)
                }
            } catch (err) {
                log.error('修改系统时间出错: ', err)
            }
        })
    }
}
