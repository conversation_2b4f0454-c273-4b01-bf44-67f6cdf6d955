const { ipcMain } = require('electron')
const KuanLockServer = require('../lock/KuAn')
const kuanLockServer = new KuanLockServer()
const JiJiaLockServer = require('../lock/JiJia')
const jiJiaLockServer = new JiJiaLockServer()
const DiChuanLockServer = require('../lock/DiChuan')
const diChuanLockServer = new DiChuanLockServer()
const LightServer = require('../light/index')
const lightServer = new LightServer()
const remoteServer = require('../server/index')
const mqttIpc = require('./mqttIpc')

let terminalConfig

module.exports = {
    initIpc(win, config) {
        terminalConfig = config
        // 外设服务
        remoteServer.init(win, config)
        const lockServerMap = {
            1: kuanLockServer,
            2: ji<PERSON>iaLockServer,
            3: diChuanLockServer
        }
        // 锁控
        ipcMain.on('handle-lock', (event, arg) => {
            switch (arg.type) {
                case 'init':
                    lockServerMap[terminalConfig.lockType].init(terminalConfig.lockCom, terminalConfig.baudRate)
                    break
                case 'openlock':
                    lockServerMap[terminalConfig.lockType].openLock(arg.lockPlate, arg.lockNumber)
                    break
                case 'checklock':
                    lockServerMap[terminalConfig.lockType].checkLockStatus(arg.lockPlate, arg.lockNumber)
                    break
                case 'setlight':
                    if (terminalConfig.lockType == 3) {
                        diChuanLockServer.handleLight(arg.actionType, arg.lockPlate, arg.lockNumber)
                    }
                    break
                case 'close':
                    try {
                        lockServerMap[terminalConfig.lockType].close()
                    } catch (err) {
                        console.error(err)
                    }
                    break
                default:
                    break
            }
        })
        jiJiaLockServer.on('lock-callback', (arg) => {
            win.webContents.send('lock-callback-result', arg)
        })
        kuanLockServer.on('lock-callback', (arg) => {
            win.webContents.send('lock-callback-result', arg)
        })
        diChuanLockServer.on('lock-callback', (arg) => {
            win.webContents.send('lock-callback-result', arg)
        })
        // 酷安和暨嘉补光灯
        ipcMain.on('handle-light', (event, arg) => {
            switch (arg.type) {
                case 'init':
                    lightServer.init(terminalConfig.lightCom, terminalConfig.lightBaudRate, terminalConfig.lightType)
                    break
                case 'close':
                    lightServer.close()
                    break
                default:
                    lightServer.handle(arg.type)
                    break
            }
        })
        lightServer.on('light-callback', (arg) => {
            win.webContents.send('light-callback-result', arg)
        })
        // mqtt IPC
        mqttIpc.init(win)
    }
}
