const LockCommon = require('./common')

// DC
class Lock extends LockCommon {
    constructor() {
        super()
        this.baudRate = 9600 // 比特率
        this.doorCount = 21 // 一个锁板最大柜数
    }
    /**
	 * 开锁
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
    openLock(lockPlate = 1, lockNumber) {
        lockNumber = parseInt(lockNumber)
        lockPlate = parseInt(lockPlate)
        if (lockNumber) {
            const dirObj = {
                lockPlate,
                lockNumber,
                dir: [0x88, lockPlate, 0x8a, 0x04, lockNumber, 0x00, 0x00, 0x00],
                time: new Date().getTime(),
                type: 'open',
                delay: 300
            }
            this.runList.push(dirObj)
            this.checkLockStatus(lockPlate, lockNumber, 'open-check')
        } else {
            this.emit('lock-callback', {
                type: 'error',
                code: 6,
                msg: this.codeDic[6]
            })
        }
    }
    /**
	 * 查询具体锁板的柜子状态
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
    checkLockStatus(lockPlate = 1, lockNumber, type = 'check') {
        lockNumber = parseInt(lockNumber)
        lockPlate = parseInt(lockPlate)
        if (lockNumber) {
            const dirObj = {
                lockPlate,
                lockNumber,
                dir: [0x88, lockPlate, 0x80, 0x01, lockNumber],
                time: new Date().getTime(),
                type,
                delay: type == 'check-light' ? 150 : 800
            }
            this.runList.push(dirObj)
            !this.timer && this.run()
        } else {
            this.emit('lock-callback', {
                type: 'error',
                code: 6,
                msg: this.codeDic[6]
            })
        }
    }
    /**
	 * 设置主柜补光灯
	 * @param {String} actionType open or close
	 * @param {Number} lockNumber 锁号
	 */
    handleLight(actionType, lockPlate = 1, lockNumber) {
        lockNumber = parseInt(lockNumber)
        lockPlate = parseInt(lockPlate)
        this.logger.info('开启补光:',lockNumber,lockPlate)
        if (lockNumber) {
            const dirObj = {
                lockPlate,
                lockNumber,
                dir: [0x88, lockPlate, 0x4a, 0x04, lockNumber, 0xff, 0xff, actionType == 'openlight' ? 0x01 : 0x00],
                time: new Date().getTime(),
                type: actionType,
                delay: 50
            }
            this.runList.push(dirObj)
            this.checkLockStatus(lockPlate, lockNumber, 'check-light')
        } else {
            this.emit('lock-callback', {
                type: 'error',
                code: 6,
                msg: this.codeDic[6]
            })
        }
    }
    /**
	 * 接收串口返回数据
	 */
    receiveMessage() {
        this.port.on('readable', () => {
            const res = this.port.read()
            if (this.backData) {
                this.backData = this.backData.concat([...res])
                this.logger.info('丢包处理:', JSON.stringify(this.backData))
            } else {
                this.backData = [...res]
                if (this.backData[0] != 0x86) {
                    this.logger.info('丢包处理:', '数据异常！')
                    this.backData = null
                    return
                }
            }
            this.logger.info('data:', JSON.stringify(this.backData))
            const dataLen = 6 + (this.backData[3] || 0)
            if (this.backData[0] == 0x86 && this.backData.length >= dataLen && this.backData[dataLen - 1] == 0x66) {
                const dirObj = this.tempList[0] || {}
                const actionType = dirObj.type ? this.getActionType(dirObj.type) : ''
                if (actionType && dirObj.timer) {
                    this.emit('lock-callback', {
                        type: 'message',
                        code: 2,
                        msg: this.codeDic[2],
                        data: {
                            actiontype: actionType,
                            box: this.backData[1],
                            door: this.backData[4],
                            result: this.backData[5] == 0x00 ? 1 : 0
                        }
                    })
                    clearInterval(dirObj.timer)
                    dirObj.timer = null
                    this.tempList.shift()
                }
                this.backData = null
            }
        })
    }
    /**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
    dealCommand(command) {
        const verifyCode = command.reduce((total, value) => {
            return total ^ value
        }, 0)
        command.push(verifyCode)
        command.push(0x68)
        const newCommand = command.map((num) => {
            return this.decToHex(num)
        })
        const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
        this.logger.info('指令:', JSON.stringify(cmdBuffer))
        return cmdBuffer
    }
}

module.exports = Lock
