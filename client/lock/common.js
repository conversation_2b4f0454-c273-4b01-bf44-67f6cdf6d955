const { EventEmitter } = require('events')
// const SerialPort = require('serialport')
const { SerialPort } = require("serialport");
const Logger = require('../log/index')
const log = new Logger('lock')
const code = {
    0: '连接正常',
    1: '连接异常',
    2: '发送指令正常',
    3: '发送指令失败',
    4: '关闭正常',
    5: '关闭失败',
    6: '参数缺失',
    7: '发送指令后无数据返回',
    8: '串口返回数据异常'
}

class LockCommon extends EventEmitter {
    constructor() {
        super()
        this.codeDic = code
        this.logger = log
        this.port = null // 串口连接对象
        this.path = ''
        this.baudRate = 9600 // 比特率
        this.doorCount = 18 // 一个锁板最大柜数
        this.runList = [] // 执行数组
        this.tempList = [] // 临时查询数组
        this.delay = 500 // 开锁指令等待时间：500ms
        this.timer = null // 轮询执行数组定时器
        this.backData = null // 记录回调数据包
    }
    /**
	 * 初始化
	 * @param {String} path com
	 */
    init(path) {
       
         SerialPort.list().then((ports) => {
                this.logger.info('串口端口列表：', JSON.stringify(ports))
            })
            .catch((err) => {
                this.logger.error('串口扫描失败：', JSON.stringify(err))
            })
            console.log(this.baudRate,'this.baudRate')
             console.log(path,'this.path串口端口列表')
        this.path = path
        this.port = new SerialPort( {path:this.path, baudRate: this.baudRate }, (err) => {
            console.log(err,'err串口端口列表')
            if (err) {
                this.logger.error('串口连接失败：', JSON.stringify(err))
                this.emit('lock-callback', {
                    type: 'open',
                    code: 1,
                    msg: this.codeDic[1]
                })
            } else {
                this.logger.info('端口打开成功！')
                this.emit('lock-callback', {
                    type: 'open',
                    code: 0,
                    msg: this.codeDic[0]
                })
                this.receiveMessage()
            }
        })
        
        this.logger.info('lock::::',this.port)
    }
    /**
	 * 执行指令
	 */
    run() {
        if (!this.port) {
            this.emit('lock-callback', {
                type: 'open',
                code: 1,
                msg: this.codeDic[1]
            })
            this.tempList.forEach((item) => {
                if (item.timer) {
                    clearInterval(item.timer)
                    item.timer = null
                }
            })
            this.tempList = []
            this.runList = []
            if (this.timer) {
                clearTimeout(this.timer)
                this.timer = null
            }
            return
        }
        // 执行队列已空
        if (this.runList.length === 0) {
            this.timer && clearTimeout(this.timer)
            this.timer = null
            return
        }
        // 已有执行定时器
        if (this.timer) {
            return
        }
        this.runTimeOut()
    }
    /**
	 * 发送指令
	 */
    runTimeOut() {
        const dirObj = JSON.parse(JSON.stringify(this.runList[0]))
        this.timer = setTimeout(() => {
            const actionType = dirObj.type ? this.getActionType(dirObj.type) : ''
            if (actionType) {
                dirObj.timer = setInterval(() => {
                    const curTime = new Date().getTime()
                    if (curTime - dirObj.time >= 10 * 1000) {
                        this.emit('lock-callback', {
                            type: 'message',
                            code: 7,
                            msg: this.codeDic[7],
                            data: {
                                actiontype: actionType,
                                box: dirObj.lockPlate,
                                door: dirObj.lockNumber,
                                result: -1 // 异常状态
                            }
                        })
                        clearInterval(dirObj.timer)
                        dirObj.timer = null
                        this.tempList.shift()
                    }
                }, 1000)
                this.tempList.push(dirObj)
            }
            this.sendMessage(dirObj)
            this.runList.shift()
            clearTimeout(this.timer)
            this.timer = null
            this.run()
        }, dirObj.delay || this.delay)
    }
    /**
	 * getActionType
	 * @param {String} type
	 * @returns String
	 */
    getActionType(type) {
        let actiontype = ''
        switch (type) {
            case 'check':
                actiontype = 'checklock'
                break
            case 'open-check':
                actiontype = 'openlock'
                break
            case 'check-light':
                actiontype = 'checklight'
                break
            default:
                break
        }
        return actiontype
    }
    /**
	 * 接收串口返回数据
	 */
    receiveMessage() {}
    /**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
    dealCommand(command) {}
    /**
	 * 向串口发送数据
	 * @param {Object} dirObj
	 * @returns Promise
	 */
    sendMessage(dirObj) {
        return new Promise((resolve, reject) => {
            this.port.write(this.dealCommand(dirObj.dir), (err) => {
                if (err) {
                    this.logger.error(`发送指令失败: `, JSON.stringify(err))
                    this.emit('lock-callback', {
                        type: 'error',
                        code: 3,
                        msg: this.codeDic[3]
                    })
                    this.tempList = this.tempList.filter((item) => {
                        if (item.time == dirObj.time) {
                            clearInterval(dirObj.timer)
                            dirObj.timer = null
                        } else {
                            return item
                        }
                    })
                    reject(false)
                }
                this.logger.info('发送指令成功: ', JSON.stringify(dirObj.dir))
                resolve(true)
            })
        })
    }
    /**
	 * 十进制 转 十六进制字符串
	 * @param {Number} num
	 * @returns
	 */
    decToHex(num) {
        let numHex = num.toString(16)
        numHex.length < 2 && (numHex = '0' + numHex)
        return numHex
    }
    /**
	 * 销毁事件监听
	 */
    removeAllEventListeners() {
        this.removeAllListeners('open')
        this.removeAllListeners('close')
        this.removeAllListeners('message')
        this.removeAllListeners('error')
    }
    /**
	 * 销毁
	 */
    close() {
        this.tempList.forEach((item) => {
            if (item.timer) {
                clearInterval(item.timer)
                item.timer = null
            }
        })
        this.tempList = []
        try {
            if (this.port) {
                this.port.close()
                this.port = null
            }
            this.logger.info('销毁连接成功')
            this.emit('lock-callback', {
                type: 'close',
                code: 4,
                msg: this.codeDic[4]
            })
        } catch (error) {
            this.logger.info('销毁连接失败:', JSON.stringify(error))
            this.emit('lock-callback', {
                type: 'close',
                code: 5,
                msg: this.codeDic[5]
            })
        }
        this.removeAllEventListeners()
    }
}
module.exports = LockCommon
