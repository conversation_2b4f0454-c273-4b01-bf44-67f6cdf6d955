const LockCommon = require('./common')

class Lock extends LockCommon {
	constructor() {
		super()
		this.baudRate = 57600 // 比特率
		this.doorCount = 18 // 一个锁板最大柜数
	}
	/**
	 * 开锁
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
	openLock(lockPlate = 1, lockNumber) {
		lockNumber = parseInt(lockNumber)
		lockPlate = parseInt(lockPlate)
		if (lockNumber) {
			const dirObj = {
				lockPlate,
				dir: [0x5a, 0xa5, 0x05, lockPlate, 0xa1, lockNumber, 0x00, 0x00],
				time: new Date().getTime(),
				type: 'open',
				delay: 500
			}
			this.runList.push(dirObj)
			this.checkLockStatus(lockPlate, lockNumber, 'open-check')
		} else {
			this.emit('lock-callback', {
				type: 'error',
				code: 6,
				msg: this.codeDic[6]
			})
		}
	}
	/**
	 * 查询具体锁板的柜子状态
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
	checkLockStatus(lockPlate = 1, lockNumber = 0, type = 'check') {
		lockNumber = parseInt(lockNumber)
		lockPlate = parseInt(lockPlate)
		const dirObj = {
			lockPlate,
			lockNumber,
			dir: [0x5a, 0xa5, 0x05, lockPlate, 0xa2, lockNumber, 0x00, 0x00],
			time: new Date().getTime(),
			type,
			delay: 500
		}
		this.runList.push(dirObj)
		!this.timer && this.run()
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			const res = this.port.read()
			if (this.backData) {
				this.backData = this.backData.concat([...res])
				this.logger.info('丢包处理:', JSON.stringify(this.backData))
			} else {
				this.backData = [...res]
			}
			this.logger.info('data:', JSON.stringify(this.backData))
			if (this.backData[0] == 0x5d && this.backData[1] == 0xd5 && this.backData.length >= 13) {
				let status = this.hanldeMessage(this.backData.slice(6, 9))
				const dirObj = this.tempList[0] || {}
				status = status.filter((item) => dirObj.lockNumber == item.lockNumber) || []
				const actionType = this.getActionType(dirObj.type)
				if (dirObj.timer && actionType != '') {
					this.emit('lock-callback', {
						type: 'message',
						code: 2,
						msg: this.codeDic[2],
						data: {
							actiontype: actionType,
							box: this.backData[3],
							door: status[0].lockNumber,
							result: status[0].status
						}
					})
					clearInterval(dirObj.timer)
					dirObj.timer = null
					this.tempList.shift()
				}
				this.backData = null
			}
		})
	}
	/**
	 * 返回数据处理
	 * @param {Array} bytes 数据数组
	 * @returns status
	 */
	hanldeMessage(bytes) {
		// 字节序倒序
		const reversed = (bytes[0] & 0xff) | ((bytes[1] & 0xff) << 8) | ((bytes[2] & 0xff) << 16)
		this.logger.info('返回数据处理:', JSON.stringify(bytes), reversed.toString(2))
		// 将低 doorCount 位转换为位，并从最低位开始返回开关状态
		let status = []
		for (let i = 0; i < this.doorCount; i++) {
			const bit = (reversed >> i) & 1
			const lockStatus = {
				lockNumber: i + 1,
				status: bit
			}
			status.push(lockStatus)
		}
		return status
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.slice(2).reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
}

module.exports = Lock
