const LockCommon = require('./common')

// 酷安
class Lock extends LockCommon {
	constructor() {
		super()
		this.baudRate = 9600 // 比特率
		this.doorCount = 18 // 一个锁板最大柜数
	}
	/**
	 * 开锁
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
	openLock(lockPlate = 1, lockNumber) {
		lockNumber = parseInt(lockNumber)
		lockPlate = parseInt(lockPlate)
		if (lockNumber >= 0 && lockNumber >= 0) {
			const dirObj = {
				lockPlate,
				lockNumber,
				dir: [0x8a, lockPlate, lockNumber, 0x11],
				time: new Date().getTime(),
				type: 'open',
				delay: 500
			}
			this.runList.push(dirObj)
			this.checkLockStatus(lockPlate, lockNumber, 'open-check')
		} else {
			this.emit('lock-callback', {
				type: 'error',
				code: 6,
				msg: this.codeDic[6]
			})
		}
	}
	/**
	 * 查询具体锁板的柜子状态
	 * @param {Number} lockPlate 锁板
	 * @param {Number} lockNumber 锁号
	 */
	checkLockStatus(lockPlate = 1, lockNumber, type = 'check') {
		lockNumber = parseInt(lockNumber)
		lockPlate = parseInt(lockPlate)
		if (lockNumber >= 0 && lockNumber >= 0) {
			const dirObj = {
				lockPlate,
				lockNumber,
				dir: [0x80, lockPlate, lockNumber, 0x33],
				time: new Date().getTime(),
				type,
				delay: 500
			}
			this.runList.push(dirObj)
			!this.timer && this.run()
		} else {
			this.emit('lock-callback', {
				type: 'error',
				code: 6,
				msg: this.codeDic[6]
			})
		}
	}
	/**
	 * 接收串口返回数据
	 */
	receiveMessage() {
		this.port.on('readable', () => {
			const res = this.port.read()
			if (![0x80, 0x8a].includes(res[0]) && !this.backData) {
				this.logger.info('res data:', JSON.stringify(res))
				this.sendData(8)
				return
			}
			if (this.backData) {
				this.backData = this.backData.concat([...res])
				this.logger.info('丢包处理:', JSON.stringify(this.backData))
			} else {
				this.backData = [...res]
			}
			if (this.backData.length >= 5) {
				this.logger.info('res', JSON.stringify(this.backData))
				this.sendData(2)
			}
		})
	}
	/**
	 * 返回数据
	 */
	sendData(code) {
		const dirObj = this.tempList[0] || {}
		const actionType = dirObj.type ? this.getActionType(dirObj.type) : ''
		if (actionType && dirObj.timer) {
			let result = -1
			if (code == 2 && [0x11, 0x00].includes(this.backData[3])) {
				result = parseInt(this.backData[3]) == parseInt(0x11) ? 1 : 0
			}
			this.emit('lock-callback', {
				type: 'message',
				code,
				msg: this.codeDic[code],
				data: {
					actiontype: actionType,
					box: result != -1 ? parseInt(this.backData[1]) : dirObj.lockPlate,
					door: result != -1 ? parseInt(this.backData[2]) : dirObj.lockNumber,
					result
				}
			})
			clearInterval(dirObj.timer)
			dirObj.timer = null
			this.tempList.shift()
		}
		this.backData = null
	}
	/**
	 * 指令数据处理
	 * @param {Array} command 指令数组
	 * @returns cmdBuffer
	 */
	dealCommand(command) {
		const verifyCode = command.reduce((total, value) => {
			return total ^ value
		}, 0)
		command.push(verifyCode)
		const newCommand = command.map((num) => {
			return this.decToHex(num)
		})
		const cmdBuffer = Buffer.from(newCommand.join(''), 'hex')
		return cmdBuffer
	}
}

module.exports = Lock
