const { ipcMain } = require('electron')
const Logger = require('../log/index')
const log = new Logger('server')
const express = require('express')
const ws = require('nodejs-websocket')
const http = express() // 初始化实例
let wsServer
let httpServer
let wsResponse
let httpResponse
var isOpen = false // 限制每次远程开柜只能是一种类型（ws or http）操作
let window = null

module.exports = {
    init(win, config) {
        window = win
        this.initHttpServer(config.remoteHttpServerProt)
        this.initWsServer(config.remoteWsServerProt)
        ipcMain.on('remote-open-lock-result', (event, result) => {
            this.callback(result)
        })
        // 兼容终端是否在操作中
        ipcMain.on('set-terminal-status', (event, arg) => {
            isOpen = arg
        })
    },
    // ws远程开柜
    initWsServer(port = 24000) {
        wsServer = ws.createServer((socket) => {
            // 读取字符串消息，事件名称为:text
            socket.on('text', (arg) => {
                log.info('服务器端收到客户端发来的消息:' + arg)
                arg = JSON.parse(arg || '{}')
                if (isOpen) {
                    socket.sendText(JSON.stringify({ result: -1, success: true, msg: '开柜失败【终端操作占用中】', actiontype: arg.actionType }))
                } else {
                    isOpen = true
                    httpResponse = null
                    wsResponse = socket
                    if ((arg.box && arg.door) || arg.lockNum) {
                        window.webContents.send('remote-open-lock', arg)
                    } else {
                        wsResponse.sendText(JSON.stringify({ result: -1, success: true, msg: '参数缺失', actiontype: arg.actionType }))
                    }
                }
            })
            socket.on('connect', (code) => {
                log.info('开启连接:', code)
            })
            socket.on('close', (code) => {
                log.info('关闭连接:', code)
                isOpen = false
            })
            socket.on('error', (code) => {
                log.info('异常关闭:', code)
                this.close()
            })
        })
        wsServer.listen(port)
        wsServer.on('listening', () => {
            // 端口未被占用
            log.info(`${port}端口，外设服务初始化成功`)
        })
        wsServer.on('error', (err) => {
            if (err.code === 'EADDRINUSE') {
                // 端口已经被使用
                log.error(`${port}端口已经被使用`)
            }
        })
    },
    // 中间件
    mv(req, res, next) {
        req.startTime = Date.now()
        res.setHeader('Content-type', 'application/json; charset=utf-8')
        next()
    },
    // http远程开柜
    initHttpServer(port = 5888) {
        http.get('/imt/api/openLock', [this.mv], (req, res) => {
            if (isOpen) {
                this.callback({ result: -1, msg: '开锁失败', actiontype: 'openlock' })
            } else {
                isOpen = true
                wsResponse = null
                httpResponse = res
                log.info('远程开柜http req.query: ', req.query)
                const { lockNum } = req.query
                if (lockNum) {
                    window.webContents.send('remote-open-lock', req.query)
                } else {
                    this.callback({ result: -1, msg: '开锁失败', actiontype: 'openlock' })
                }
            }
        })
        httpServer = http.listen(port, (err) => {
            if (err) {
                log.error(`${port}端口已经被使用`)
            } else {
                log.info(`http服务已开启, port: ${port}`)
            }
        })
    },
    // 服务回调
    callback(result) {
        isOpen = false
        if (httpResponse) {
            log.info('HTTP 远程开柜操作结果:', JSON.stringify(result))
            httpResponse.send(result)
            httpResponse = null
        }
        if (wsResponse) {
            log.info('WS 远程开柜操作结果:', JSON.stringify(result))
            wsResponse && wsResponse.sendText(JSON.stringify(result))
            wsResponse = null
        }
    },
    // 销毁服务
    close() {
        try {
            isOpen = false
            httpServer && httpServer.close()
            wsServer && wsServer.close()
        } catch (error) {
            log.error('外设服务销毁失败：' + error)
        }
    }
}
