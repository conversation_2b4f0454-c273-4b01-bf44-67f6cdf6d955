const { ipcMain, app } = require('electron')
const http = require('http')
const fs = require('fs')
const path = require('path')
const url = require('url')
const serveStatic = require('serve-static')
const archiver = require('archiver')
const querystring = require('querystring')
const Updater = require('../upgrad/index')
const tools = require('../mqtt/tools')
const Logger = require('../log/index')

const updater = new Updater()
const log = new Logger('config-server')

const packagePath = process.platform == 'win32' ? 'C:\\pymClientTerminalPackage' : '/opt/gosuncn/pymClientTerminalPackage'
const port = 9587
const baseUrl = '/pureWeb'

// 中间件配置静态资源目录，ps:开发环境也可以使用webpack启动的服务访问
const staticFile = serveStatic(app.isPackaged ? path.join(process.resourcesPath, 'app.asar/dist') : 'http://*************:9981/', {
	index: ['web.html']
})
// 获取app配置
const getAppConfig = (isGetDevices, callback) => {
	global.mainWin.webContents.send('get-local-config', { isGetDevices })
	const getLocalConfigResult = (event, result) => {
		ipcMain.removeAllListeners('get-local-config-result')
		callback && callback(JSON.parse(result))
	}
	ipcMain.on('get-local-config-result', getLocalConfigResult)
}
// 获取配置列表
const configList = (req, res) => {
	getAppConfig(true, (result) => {
		const data = { code: 200, msg: '成功', data: result }
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify(data))
	})
}
// 设置本地配置
const setLocalConfig = (req, res) => {
	let rawData = ''
	console.log('-------============================================ddd')
	req.on('data', (chunk) => {
		rawData += chunk
	})
	req.on('end', () => {
		res.writeHead(200, { 'Content-Type': 'application/json' })
		try {
			const remoteResult = JSON.parse(rawData)
			// 写入本地配置
			log.info('远程配置接口需要更新的数据：', remoteResult)
			global.mainWin.webContents.send('set-local-config', JSON.stringify(remoteResult))
			res.end(JSON.stringify({ code: 200, msg: '设置成功！', data: null }))
		} catch (error) {
			log.info('远程配置接口异常：', error)
			res.end(JSON.stringify({ code: 5000, msg: '设置失败！', data: null }))
		}
	})
}
// 重启客户端
const reloadApp = (req, res) => {
	try {
		tools.rebootTerminal()
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify({ code: 200, msg: '重启成功！', data: null }))
	} catch (error) {
		log.info('reloadApp error:', error)
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify({ code: 500, msg: '重启失败！', data: null }))
	}
}
// 重启设备
const reboot = (req, res) => {
	try {
		tools.reboot()
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify({ code: 200, msg: '重启成功！', data: null }))
	} catch (error) {
		log.info('reboot error:', error)
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify({ code: 500, msg: '重启失败！', data: null }))
	}
}
// 获取日志文件
const getLogs = (req, res) => {
	const archiveName = 'log.zip'
	const zipPath = path.join(global.basePath, archiveName)
	const directory = global.pymClientDataPath
	const output = fs.createWriteStream(zipPath)
	const archive = archiver('zip', {
		zlib: { level: 9 } // 压缩级别
	})
	// 监听输出流关闭事件
	output.on('close', () => {
		const fileStream = fs.createReadStream(zipPath)
		// 设置响应头
		res.writeHead(200, {
			'Content-Disposition': `attachment; filename=${archiveName}`,
			'Content-Type': 'application/octet-stream'
		})
		// 将输出流绑定到HTTP响应
		fileStream.pipe(res)
		log.info(`log目录压缩完成，共：${(archive.pointer() / 1024 / 1024).toFixed(2)}M`, 'success')
	})
	// 监听压缩过程中的错误
	archive.on('error', (err) => {
		throw err
	})
	// 将输出流绑定到归档
	archive.pipe(output)
	// 添加目录下所有文件到压缩文件
	archive.directory(directory, false)
	// 完成归档
	archive.finalize()
}
// 获取终端配置
const getTerminalConfig = (req, res) => {
	const { ip, mac } = tools.getIpMAC()
	getAppConfig(false, (result) => {
		const { config } = result
		const data = {
			code: 200,
			msg: '成功',
			data: {
				localIp: config.localIp || ip,
				localDNS: config.localDNS,
				localGetway: config.localGetway,
				localNetmask: config.localNetmask,
				localMac: config.localMac || mac
			}
		}
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify(data))
	})
}
// 设置终端配置
const setTerminalConfig = (req, res) => {
	getAppConfig(false, (result) => {
		const { config } = result
		let rawData = ''
		req.on('data', (chunk) => {
			rawData += chunk
		})
		req.on('end', () => {
			res.writeHead(200, { 'Content-Type': 'application/json' })
			try {
				const remoteResult = JSON.parse(rawData)
				if (process.platform != 'linux') {
					res.end(JSON.stringify({ code: 500, msg: '不支持网络设置', data: null }))
					return
				}
				if (remoteResult.localIp != config.localIp || remoteResult.localDNS != config.localDNS || remoteResult.localGetway != config.localGetway || remoteResult.localNetmask != config.localNetmask) {
					tools.setLocalNetwork(remoteResult.localIp, remoteResult.localDNS, remoteResult.localGetway, remoteResult.localNetmask)
				}
				global.mainWin.webContents.send('set-network-config', JSON.stringify(remoteResult))
				res.end(JSON.stringify({ code: 200, msg: '设置成功！', data: null }))
			} catch (error) {
				log.info('网络配置接口异常：', error)
				res.end(JSON.stringify({ code: 5000, msg: '网络设置失败！', data: null }))
			}
		})
	})
}
// 获取属性
const getProperty = async (req, res) => {
	// 解析URL
	const parsedUrl = url.parse(req.url)
	// 解析查询字符串参数
	const queryParams = querystring.parse(parsedUrl.query)
	log.info('getProperty', queryParams)
	// 查询属性数组
	const queryProps = queryParams.searchProps ? queryParams.searchProps.split(',') : []
	// 系统可查询属性
	const sysProps = ['version', 'ip', 'mac', 'deviceManage', 'threadNumber', 'cpuName', 'memory', 'free', 'used', 'sysVersion', 'sysPlatform', 'sysArch', 'cpuUsed']
	const appProps = ['idleBox', 'totalBox', 'model', 'name']
	// 返回数据
	let R = {}
	// 获取系统配置信息
	const getSysProps = (props = []) => {
		return new Promise(async (resolve, reject) => {
			const properties = await tools.getProperty(props)
			resolve(properties)
		})
	}
	// 获取app renderer配置信息
	const getAppProps = () => {
		global.mainWin.webContents.send('get-property')
		return new Promise(async (resolve, reject) => {
			const getPropertyResult = (event, result) => {
				ipcMain.removeAllListeners('get-property-result')
				resolve(result)
			}
			ipcMain.on('get-property-result', getPropertyResult)
		})
	}
	// 接口返回
	const response = () => {
		const data = { code: 200, msg: '成功', data: R }
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify(data))
	}
	if (queryProps.length) {
		// 按需查询
		let sysList = []
		let appList = []
		queryProps.forEach((prop) => {
			if (sysProps.includes(prop)) {
				sysList.push(prop)
			} else if (appProps.includes(prop)) {
				appList.push(prop)
			} else {
				R[prop] = ''
			}
		})
		sysList.length && Object.assign(R, await getSysProps(sysList))
		const appResult = await getAppProps()
		appList.forEach((prop) => {
			R[prop] = appResult[prop]
		})
		response()
	} else {
		// 查询全部
		Object.assign(R, await getSysProps())
		Object.assign(R, await getAppProps())
		response()
	}
}
// 下发升级
const downUpgrade = async (req, res) => {
	global.mainWin.webContents.send('down-upgrade')
	const downUpgradeResult = (event, result) => {
		ipcMain.removeAllListeners('down-upgrade-result')
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify(result))
	}
	ipcMain.on('down-upgrade-result', downUpgradeResult)
}
// 接受文件流
const upload = (req, res) => {
	req.setEncoding('binary')
	createDir(packagePath)
	let body = '' // 文件数据
	let fileName = '' // 文件名
	// 边界字符串
	let boundary = req.headers['content-type'].split('; ')[1].replace('boundary=', '')
	req.on('data', (chunk) => {
		body += chunk
	})
	req.on('end', () => {
		res.writeHead(200, { 'Content-Type': 'application/json' })
		let file = querystring.parse(body, '\r\n', ':')
		//获取文件名
		let fileInfo = file['Content-Disposition'].split('; ')
		for (const value in fileInfo) {
			if (fileInfo[value].indexOf('filename=') != -1) {
				fileName = fileInfo[value].substring(10, fileInfo[value].length - 1)

				if (fileName.indexOf('\\') != -1) {
					fileName = fileName.substring(fileName.lastIndexOf('\\') + 1)
				}
			}
		}
		let entireData = body.toString()
		const contentType = file['Content-Type'].substring(1)
		//获取文件二进制数据开始位置，即contentType的结尾
		let upperBoundary = entireData.indexOf(contentType) + contentType.length
		let shorterData = entireData.substring(upperBoundary)
		// 替换开始位置的空格
		let binaryDataAlmost = shorterData.replace(/^\s\s*/, '').replace(/\s\s*$/, '')
		// 去除数据末尾的额外数据，即: "--"+ boundary + "--"
		let binaryData = binaryDataAlmost.substring(0, binaryDataAlmost.indexOf('--' + boundary + '--'))
		log.info('======接收文件的路径=====', `${packagePath}/${fileName}`)
		// 保存文件
		fs.writeFile(`${packagePath}/${fileName}`, binaryData, 'binary', (err) => {
			if (err) {
				log.error('=======文件上传失败======', err)
				res.end(JSON.stringify({ code: 500, msg: '文件上传失败', data: null }))
				return
			}
			res.end(
				JSON.stringify({
					code: 200,
					msg: '文件上传成功',
					data: { fileName }
				})
			)
		})
	})
}
/**
 * 判断目录是否存在，不存在则创建
 * recursive: 表示多层目录时递归创建
 */
function createDir(path) {
	if (!fs.existsSync(path)) {
		fs.mkdirSync(path, { recursive: true })
	}
}
// 安装包升级
const upgrade = (req, res) => {
	req.on('data', async (chunk) => {
		res.writeHead(200, { 'Content-Type': 'application/json' })
		const params = JSON.parse(chunk.toString())
		await updater.downloadAfter()
		updater.install(params.fileName)
		res.end(JSON.stringify({ code: 200, msg: '升级成功！', data: null }))
	})
}
const interceptReq = (req, res) => {
	let rawData = ''
	req.on('data', async (chunk) => {
		rawData += chunk
	})
	req.on('end', () => {
		res.writeHead(200, { 'Content-Type': 'application/json' })
		res.end(JSON.stringify({ code: 5001, msg: '终端正在升级中，请稍后重试，谢谢！', data: null }))
	})
}
module.exports = {
	init() {
		// 创建http服务
		const httpServer = http.createServer((req, res) => {
			staticFile(req, res, () => {
				const parseUrl = url.parse(req.url, true)
				res.setHeader('Cache-Control', 'no-cache, no-store, max-age=0, must-revalidate')
				if (global.upgradeStatus) {
					return interceptReq(req, res)
				}
				switch (parseUrl.pathname) {
					case `${baseUrl}/configList`:
						configList(req, res)
						break
					case `${baseUrl}/setLocalConfig`:
						setLocalConfig(req, res)
						break
					case `${baseUrl}/terminalConfig`:
						getTerminalConfig(req, res)
						break
					case `${baseUrl}/setTerminalConfig`:
						setTerminalConfig(req, res)
						break
					case `${baseUrl}/reloadApp`:
						reloadApp(req, res)
						break
					case `${baseUrl}/reboot`:
						reboot(req, res)
						break
					case `${baseUrl}/getLogs`:
						getLogs(req, res)
						break
					case `${baseUrl}/getProperty`:
						getProperty(req, res)
						break
					case `${baseUrl}/downUpgrade`:
						downUpgrade(req, res)
						break
					case `${baseUrl}/upload`:
						upload(req, res)
						break
					case `${baseUrl}/upgrade`:
						upgrade(req, res)
						break
					default:
						break
				}
			})
		})
		httpServer.listen(port, '', () => {
			log.info(`http服务已启动，启动端口为：${port}`, 'success')
		})
	}
}
