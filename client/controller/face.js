const path = require('path')
const ffi = require('ffi-napi')
const { ref, int, intPtr, float, floatPtr, char, charPtr, int64, int64Ptr, ucharPtr, FaceDetectFilterParams, FaceTrackedRect, PersonInfo, responseType } = require('./structType.js')
const { ipcMain, app } = require('electron')
const Logger = require('../log/index')
const log = new Logger('face')

class FaceController {
	constructor() {
		this.resourcesPath = '/faceAuth'
		this.extraResources = path.join(app.isPackaged ? process.resourcesPath : process.cwd(), 'extraResources')
		this.faceMethods = null
		this.faceDetectId = null
		this.initState = false
	}
	/**
	 * 初始化面部识别 SDK
	 */
	init() {
		ipcMain.on('init_face_config', () => {
			this.initFaceAuthFunc()
			this.initSDK()
		})
		/**
		 * params
		 * params.base64 base64图片
		 */
		ipcMain.on('face_recognition_path', (event, params) => {
			if (!params.base64) {
				log.info('图片信息不能为空！', '人脸识别错误')
				event.returnValue = null
				return
			}
			try {
				const resultImagData = this.handleFaceDetect(params)
				console.log(resultImagData)

				if (resultImagData) {
					const { x, y, width, height } = resultImagData
					event.returnValue = {
						x,
						y,
						width,
						height
					}
				}
				event.returnValue = null
			} catch (e) {
				log.info(e, '人脸识别错误12')
				event.returnValue = null
			}
		})
		/**
		 * 销毁人脸检测容器
		 */
		ipcMain.on('destory_face_detector', () => {
			this.faceMethods.releaseSDK()
		})
	}

	/**
	 * 初始化人脸检测方法
	 *
	 * 函数执行顺序一览
	 * 1、初始化 SDK 进程         ------->  GoNc_FaceRecognition_Init
	 * 2、创建人脸识别检测器       ------->  GoNc_FaceRecognition_Create
	 * 3、导入人脸库，以便后期比对  ------->  GoNc_FaceRecognition_ImportPersons
	 *
	 * 业务逻辑
	 * 分为临时储物与管理员储物，具体需询问产品具体逻辑
	 *
	 * 清理逻辑，仅在退出主程序后进行
	 * 1、释放检测器              ------->  GoNc_FaceRecognition_Release
	 * 2、清理 SDK 进程           ------->  GoNc_FaceRecognition_CleanUp
	 * 
	 * 细节项
	 * 1、声明自定义的指针 用 let，不要用 const
	 * 2、注意 sdk 函数内出参的类型，一定要用对
	 * 3、建议集成 sdk 方法时，使用 try catch 方法嵌套，并将错误日志通过 log 方法输出
	 */
	initFaceAuthFunc() {
		const faceSDK = path.join(this.extraResources, this.resourcesPath, 'libGoNcFaceRegSDK.so')
		log.info('faceSDK资源文件路径', faceSDK)
		try {
			this.faceMethods = new ffi.Library(faceSDK, {
				// SDK全局初始化
				GoNc_FaceRecognition_Init: [
					'int',
					[
						'string' // License文件路径
					]
				],
				// 清理进程资源
				GoNc_FaceRecognition_CleanUp: ['int', []],
				// 创建人脸检测器
				GoNc_FaceRecognition_Create: [
					'int',
					[
						char, // 模型所在文件夹的路径
						int64Ptr // 创建获得的实例
					]
				],
				// 销毁检测器
				GoNc_FaceRecognition_Release: [
					'int',
					[
						int64 //需释放的实例
					]
				],
				// 将YUV-NV21图像转换为BGR
				GoNc_FaceRecognition_Nv21Tbgr: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ucharPtr, // YUV-NV21格式的图像
						ucharPtr, // BGR格式的图像
						int, // 图像的宽度
						int // 图像的高度
					]
				],
				// 人脸检测
				GoNc_FaceRecognition_FaceDetect: [
					'int', // 返回值
					[
						int64, // 使用与hHandle对应的人脸识别器
						ucharPtr, // bgr格式的图像指针
						int, // 图像的宽度
						int, // 图像的高度
						ref.refType(FaceDetectFilterParams), // 人脸检测参数
						int, // 运行该算法调用的线程数量
						ref.refType(FaceTrackedRect), // 人脸检测信息数组
						intPtr // 人脸个数
					]
				],
				// 活体检测
				GoNc_FaceRecognition_AntiSpoofingProcess: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ucharPtr, // bgr格式的图像
						int, // 图像的宽度
						int, // 图像的高度
						ref.refType(FaceTrackedRect), // 需进行活体判定的人脸对应的检测信息
						int, // 运行该算法调用的线程数量
						intPtr // 判定结果，0代表活体，1代表假体
					]
				],
				// 双目活体检测，用于测试当前人脸是否为活体
				GoNc_FaceRecognition_AntiSpoofingProcess_DoubleSensor: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ucharPtr, // bgr格式的图像指针
						int, // 图像的宽度
						int, // 图像的高度
						ref.refType(FaceTrackedRect), // 需进行活体判定的BGR图像的人脸对应的检测信息，由调用人脸检测接口得到。
						ucharPtr, // 红外图像(三通道)数据指针
						int, // 红外图像的宽度
						int, // 红外图像的高度
						ref.refType(FaceDetectFilterParams), // 红外图像人脸检测参数
						int // 运行该算法调用的线程数量
					]
				],
				// 人脸特征提取，用于提取指定人脸的表征特征
				GoNc_FaceRecognition_GetFaceFeature: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ucharPtr, // bgr格式的图像指针
						int, // 图像的宽度
						int, // 图像的高度
						ref.refType(FaceTrackedRect), // 需进行特征提取的人脸对应的检测信息
						int, // 运行该算法调用的线程数量
						charPtr, // 人脸特征向量
						intPtr // 人脸特征长度
					]
				],
				// 人脸比对，用于对2个人脸特征进行比对，得到相似度
				GoNc_FaceRecognition_CompareFeature: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						charPtr, // 人脸特征向量1
						intPtr, // 人脸特征向量1的长度
						charPtr, // 人脸特征向量2
						intPtr, // 人脸特征向量2的长度
						floatPtr // 相似度
					]
				],
				// 面部遮挡检测 SDK 没有该方法，但文档提供了。
				// GoNc_FaceRecognition_OcclusionFaceDet: [
				// 	'int',
				// 	[
				// 		int64, // 使用与hHandle对应的人脸识别器
				// 		charPtr, // bgr格式的图像指针
				// 		int, // 图像的宽度
				// 		charPtr, // 人脸特征向量2
				// 		ref.refType(FaceTrackedRect), // 需进行特征提取的人脸对应的检测信息
				// 		int, // 运行该算法调用的线程数量
				// 		int // occlusionResult 人脸遮挡结果 0有 1无
				// 	]
				// ],
				// 人员注册库导入函数，用于一次性加载整个注册库
				GoNc_FaceRecognition_ImportPersons: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ref.refType(PersonInfo), // 人员信息库（数组的形态），单个人信息见PersonInfo，主要包含id和特征
						int // 人员数
					]
				],
				// 清空人员注册库
				GoNc_FaceRecognition_ClearPersons: [
					'int',
					[
						int64 // 使用与hHandle对应的人脸识别器
					]
				],
				// 新增人员
				GoNc_FaceRecognition_AddPerson: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ref.refType(PersonInfo), // 入库的人员信息，单个人信息见PersonInfo，主要包含id和特征
						float // 重复注册判断阈值，当注册库中存在与输入人员特征相似都超过判断阈值的，则视为重复注册，将不入库
					]
				],
				// 删除人员
				GoNc_FaceRecognition_DeletePerson: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						int // 要求删除的人员id
					]
				],
				// 更新指定人员特征
				GoNc_FaceRecognition_UpdatePersonFeature: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						ref.refType(PersonInfo) // 根据id更新对应人员的特征
					]
				],
				// 搜索人员
				GoNc_FaceRecognition_SearchPerson: [
					'int',
					[
						int64, // 使用与hHandle对应的人脸识别器
						charPtr, // 用于检索的特征向量
						int, // 用于检索的特征向量长度
						float, // 阈值，大于该阈值的人员才可能被输出
						int, // topN，最多输出大于设定阈值的人员数
						ref.refType(PersonInfo), // 检索结果，数组，每个成员定义见PersonSearchInfo（主要是id和相似度）,按照相似度从高到低排序
						intPtr // 检索出的人数
					]
				]
			})
		} catch (err) {
			log.info('初始化sdk方法失败', err)
		}
		console.log('this.faceMethods', this.faceMethods)
	}

	/**
	 * 调用sdk内部初始化方法
	 */
	initSDK() {
		try {
			const result = this.faceMethods.GoNc_FaceRecognition_Init(null)
			log.info('人脸检测器初始化结果', responseType[result])
			console.log('人脸检测器初始化结果', responseType[result])
			this.initState = !result
			if (result !== 0) {
				// 需要回调vue内弹窗方法
				return console.log('人脸识别SDK初始化失败，请退出软件重试，或联系管理员。')
			}
			this.createFaceDetect()
		} catch (err) {
			log.info('调用sdk内部初始化方法失败', err)
		}
	}

	/**
	 * 创建人脸检测器
	 */
	createFaceDetect() {
		// 如果没有初始化人脸检测器就初始化sdk
		if (!this.initState) this.initSDK()
		// 创建人脸检测器
		try {
			let tempIdPtr = ref.alloc('longlong')
			const modelPath = path.join(this.extraResources, this.resourcesPath, '/models')
			log.info('modelPath', modelPath)
			const res = this.faceMethods.GoNc_FaceRecognition_Create(modelPath, tempIdPtr)
			log.info('创建人脸检测器结果', responseType[res])
			if (res === 0) {
				this.faceDetectId = tempIdPtr.deref()
				console.log('创建人脸检测器成功，模型Id为', this.faceDetectId)
			} else {
				console.log('创建人脸检测器失败，请退出软件重试，或联系管理员。')
			}
		} catch (error) {
			log.info('创建人脸检测器失败', error)
		}
	}

	/**
	 * 释放进程并清除监听事件
	 */
	releaseSDK() {
		if (this.faceDetectId) {
			try {
				const releaseSDKResult = this.faceMethods.GoNc_FaceRecognition_Release(this.faceDetectId)
				log.info('释放人脸检测器结果', responseType[releaseSDKResult])
				try {
					const cleanUpResult = this.faceMethods.GoNc_FaceRecognition_CleanUp()
					log.info('清理进程结果', responseType[cleanUpResult])
					// 清楚监听事件
					ipcMain.removeAllListeners('init_face_config')
					ipcMain.removeAllListeners('face_recognition_path')
					ipcMain.removeAllListeners('destory_face_detector')
				} catch (err) {
					log.info('清理进程结果失败,' + err)
				}
			} catch (error) {
				log.info('释放人脸检测器失败,' + error)
			}
		} else {
			log.info('释放人脸检测器失败，未找到有效的检测器')
		}
	}

	/**
	 * 检测人脸
	 */
	handleFaceDetect({ base64, width, height }) {
		let faceCountPtr = ref.alloc('int')
		try {
			const result = this.faceMethods.GoNc_FaceRecognition_FaceDetect(this.faceDetectId, base64?.replace(/^data:image\/\w+;base64,/, ''), width, height, FaceDetectFilterParams, 2, FaceTrackedRect, faceCountPtr)
			if (result === 0) {
				const faceCount = faceCountPtr.deref()
				console.log('共检测到' + faceCount + '人脸')
			} else {
				console.log('检测人脸失败，' + responseType[result])
			}
		} catch (err) {
			console.log('检测人脸异常，' + err)
		}
	}
}

module.exports = FaceController
