const FaceController = require('./face')
const oskApi = require('./osk') // 系统软键盘
const faceInstance = new FaceController();
let isunInit = true

function initDll(initArray) {
	// 确保已经反初始化后才可调用初始化
	if (!isunInit) return
	if (initArray.includes('face')) faceInstance.init()
	if (initArray.includes('osk') && process.platform === 'win32') oskApi.initDll()
	isunInit = false
}
function unitDll(unitArray) {
	isunInit = true
	if (unitArray.includes('face')) faceInstance.releaseSDK()
}
module.exports = { initDll, unitDll }
