const path = require('path')
const { ipcMain, app } = require('electron')
const ffi = require('ffi-napi')
const oskdll = path.join(app.isPackaged ? process.resourcesPath : process.cwd(), '/extraResources/osk', 'CallOsk.dll')

let oskInstance = null
const oskApi = {
	initDll() {
		try {
			oskInstance = new ffi.Library(oskdll, {
				// 文件内的方法和参数类型
				CallOsk: ['int', []]
			})

			ipcMain.on('osk-open-dll', (event, arg) => {
				const result = oskInstance.CallOsk()
				event.reply('reply-osk-open', { result: result })
			})
		} catch (error) {
			console.log('error', error)
		}
	}
}
module.exports = oskApi
