/**
 * 该文件为libGoNcFaceRegSDK及相关SDK专属结构体
 */
const ref = require('ref-napi')
const StructType = require('ref-struct-di')(ref)
const ArrayType = require('ref-array-di')(ref)
const FR_POINT_NUM = 5;

// 导出基本类型
const { int, float, char, longlong: int64, uchar } = ref.types
const intPtr = ref.refType(int)
const floatPtr = ref.refType(float)
const int64Ptr = ref.refType(int64)
const charPtr = ref.refType(char)
const ucharPtr = ref.refType(uchar)

// 导出响应类型
const responseType = {
	0: '成功',
	1: '未初始化',
	2: '未知错误',
	3: '输入参数有误',
	4: '人脸识别算法库未注册',
	5: '实例不存在',
	6: '系统资源不足',
	7: '人脸对齐失败',
	8: '人脸检测失败',
	9: '人脸特征提取失败',
	10: '重复注册',
	11: '人员不存在'
}

// 浮点类型的点坐标
const GoPoint2f = StructType({
	fX: float, // X轴
	fY: float // Y轴
})

// 人脸姿态
const FacePose = StructType({
	fHorizontal_threshold: float // 人脸水平偏转程度限制，范围是0到1，可用此阈值过滤测脸，可设置0.6。阈值越大，返回的人脸越是正脸。
})

// 人脸检测参数
// 主要用于设置必要信息，主要包含感兴趣区域、最大最小人脸限制、姿态估计阈值以及流类型。
// 备注：
// a.	全屏抓拍时，感兴趣区域设置为0,0,width,height;
// b.	最小人脸、最大人脸一般设置为40、5000，可根据实际场景调整；
// c.	姿态估计阈值一般设置为0.6，可根据实际场景调整。
// d.	注意感兴趣区域设置不能超过图片大小，即是(nX + nWidth)小于图片宽度和(nY + nHeight）小于图片高度
const FaceDetectFilterParams = StructType({
	nX: int, // 感兴趣区域左上角横坐标
	nY: int, // 感兴趣区域左上角纵坐标
	nWidth: int, // 感兴趣区域宽度
	nHeight: int, // 感兴趣区域高度
	nMin_size: int, // 最小人脸
	nMax_size: int, // 最大人脸
	pose_thresold: ref.refType(FacePose), // 姿态估计阈值
	nStream_type: int // 流类型：0表示视频流(带跟踪id号)，1表示图片流
})

// 人脸检测信息
const FaceTrackedRect = StructType({
	nX: int, // 人脸检测框左上角横坐标
	nY: int, // 人脸检测框左上角纵坐标
	nWidth: int, // 人脸检测框宽度
	nHeight: int, // 人脸检测框高度
	nID: int, // 人脸跟踪ID，仅在流类型为视频流时生效
	face_points: ArrayType(GoPoint2f, FR_POINT_NUM) // 人脸关键点集，FR_POINT_NUM为5
})

// 注册库中的人员信息
const PersonInfo = StructType({
	person_id_: int, // 人员ID
	face_feature: ArrayType(char, 512) // 人脸特征 signed char[512]
})

// 检索信息
const PersonSearchInfo = StructType({
	person_id_: int, // 人员ID
	similarity_: float // 相似度
})

// 浮点类型的点坐标
const FR_POINTF = StructType({
	x: float, // X坐标
	y: float // Y坐标
})

// 整数类型矩形区域
const FR_RECT = StructType({
	x: int, // X坐标
	y: int, // Y坐标
	width: int, // 区域宽度
	height: int // 区域高度
})

// 浮点类型矩形区域
const FR_RECT2F = StructType({
	x: float, // X坐标
	y: float, // Y坐标
	width: float, // 区域宽度
	height: float // 区域高度
})

const GoNcFaceOcclusion = {
	GoNC_OCCLUSION: 0, // 遮挡
	GoNC_NO_OCCLUSION: 1 //非遮挡
}

// GoNcFaceRegSDK.H 文件中不需要以下结构体
// // 人脸抓拍输入参数
// export const FR_DETE_PARAM = StructType({
// 	x: int, // 感兴趣区域左上角横坐标
// 	y: int, // 感兴趣区域左上角纵坐标
// 	width: int, // 感兴趣区域宽度
// 	height: int, // 感兴趣区域高度
// 	minFace: int, // 最小人脸
// 	maxFace: int, // 最大人脸
// 	pose_thresold: FacePose, // 姿态估计阈值
// 	AmbiguitThresold: int, // 人脸质量衡量参数【0-100】默认60
// 	streamType: int // 流类型：0表示视频流(带跟踪id号)，1表示图片流
// })

// // 人脸抓拍人输出结果
// export const FR_TRACK_RECT = StructType({
// 	x: int, // 人脸检测框左上角X坐标
// 	y: int, // 人脸检测框左上角Y坐标
// 	width: int, // 人脸检测框宽度
// 	height: int, // 人脸检测框高度
// 	ID: int, // 人脸跟踪ID，仅在流类型为视频流时生效
// 	faceFeaturePoints: ref.refType(FR_POINTF[5]) // 人脸关键点集 FR_POINTF[FR_POINT_LEN]
// })

module.exports = {
	ref,
	int,
	float,
	char,
	int64,
	uchar,
	intPtr,
	floatPtr,
	int64Ptr,
	charPtr,
	ucharPtr,
	responseType,
	GoPoint2f,
	FacePose,
	FaceDetectFilterParams,
	FaceTrackedRect,
	PersonInfo,
	PersonSearchInfo,
	FR_POINTF,
	FR_RECT,
	FR_RECT2F,
	GoNcFaceOcclusion
}