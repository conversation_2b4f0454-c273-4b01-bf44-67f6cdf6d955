<!DOCTYPE html>
<html lang="en">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<title>加载异常...</title>
		<style>
			* {
				padding: 0;
				margin: 0;
			}
			html,
			body {
				height: 100%;
				width: 100%;
				margin: 0;
				background: #ffffff;
			}
			body .loading-box {
				height: -webkit-fill-available;
				background: url('./error.png') no-repeat center;
			}
			body .loading-box .icon-clsoe {
				position: absolute;
				right: 10px;
				top: 10px;
				width: 20px;
				height: 20px;
				background: url('./icon-close.png') no-repeat center;
			}
		</style>
	</head>
	<body>
		<div class="loading-box">
			<div class="icon-clsoe" id="closeBtn"></div>
		</div>
	</body>
	<script type="text/javascript">
		let closeBtnDom = document.getElementById('closeBtn')
		closeBtnDom.addEventListener('click', function() {
			const ipcRenderer = window.electronAPI?.ipcRenderer || null
			ipcRenderer && ipcRenderer.send('close-loading-error-dialog')
		})
	</script>
</html>
