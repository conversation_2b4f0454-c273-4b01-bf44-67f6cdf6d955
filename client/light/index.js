const { EventEmitter } = require('events')
// const SerialPort = require('serialport')
const { SerialPort } = require("serialport");
const Logger = require('../log/index')
const log = new Logger('light')

const code = {
    0: '连接正常',
    1: '连接异常',
    2: '发送指令正常',
    3: '发送指令失败',
    4: '关闭正常',
    5: '关闭失败'
}
const dir = {
    1: {
        open: '01050000FF008C3A',
        close: '010500000000CDCA'
    },
    2: {
        open: '01050002FF002DFA',
        close: '0105000200006C0A'
    }
}
class Light extends EventEmitter {
    constructor() {
        super()
        this.port = null
        this.baudRate = 9600
        this.com = 'COM1'
        this.manufacturer = 1
    }
    init(com, baudRate, manufacturer) {
        this.com = com
        this.baudRate = parseInt(baudRate)
        this.manufacturer = manufacturer
        this.port = new SerialPort({path:this.com, baudRate: this.baudRate }, (err) => {
            if (err) {
                console.log('串口连接失败：', err)
                this.callback({
                    type: 'open',
                    code: 1,
                    msg: code[1]
                })
            } else {
                console.log('端口打开成功！')
                this.callback({
                    type: 'open',
                    code: 0,
                    msg: code[0]
                })
            }
        })
        log.info('light::::',this.port)

    }
    handle(type) {
        if (!this.port) {
            this.callback({
                type: 'open',
                code: 1,
                msg: code[1]
            })
            return
        }
        if (['openlight', 'closelight'].includes(type)) {
            switch (type) {
                case 'openlight':
                    this.port.write(dir[this.manufacturer].open, 'hex', (err) => {
                        this.callback({
                            type: err ? 'error' : 'message',
                            code: err ? 3 : 2,
                            msg: err ? code[3] : code[2]
                        })
                    })
                    break
                case 'closelight':
                    this.port.write(dir[this.manufacturer].close, 'hex', (err) => {
                        this.callback({
                            type: err ? 'error' : 'message',
                            code: err ? 3 : 2,
                            msg: err ? code[3] : code[2]
                        })
                    })
                    break
                default:
                    break
            }
        }
    }
    callback(data) {
        log.info(data,'callbackcallback')
        this.emit('light-callback', data)
    }
    close() {
        log.error('补光灯连接',this.port)
        if (this.port) {
            try {
                this.port.write(dir[this.manufacturer].close, 'hex', (err) => {
                    if (err) {
                        log.error('销毁补光灯连接，关闭补光灯失败')
                    }
                    this.port.close()
                    this.port = null
                    this.callback({
                        type: 'close',
                        code: 4,
                        msg: code[4]
                    })
                })
            } catch (error) {
                this.callback({
                    type: 'close',
                    code: 5,
                    msg: code[5]
                })
            }
        } else {
            log.error('销毁补光灯连接失败')
            this.callback({
                type: 'open',
                code: 1,
                msg: code[1]
            })
        }
    }
}

module.exports = Light
