const { app } = require('electron')
const { exec } = require('child_process')
const os = require('os')
const si = require('systeminformation')
const { actualVersion } = require('../../package.json')
const Logger = require('../log/index')
const log = new Logger('tools-local')
const zeroRegex = /(?:[0]{1,2}[:-]){5}[0]{1,2}/

module.exports = {
	/**
	 * 获取Mac地址
	 */
	getMacAddress() {
		const { mac } = this.getIpMAC()
		return typeof mac == 'string' ? mac.replace(/:/g, '') : ''
	},
	/**
	 * 获取ip、mac地址
	 */
	getIpMAC() {
		const list = os.networkInterfaces()
		for (const [key, parts] of Object.entries(list)) {
			if (!parts) continue
			for (const part of parts) {
				if (zeroRegex.test(part.mac) === false && part.family == 'IPv4' && !part.internal) {
					return { ip: part.address, mac: part.mac }
				}
			}
		}
		// throw new Error('获取IP、MAC地址失败')
		return { ip: '', mac: '' }
	},
	/**
	 * 设置本机网络
	 */
	setLocalNetwork(ip = '', dns = '', gateway = '', netmask = '24') {
		const ethX = 'con-eth0'
		if (process.platform == 'linux') {
			const cmd = `sudo nmcli connection modify ${ethX} ipv4.addresses ${ip}/${netmask} ipv4.dns ${dns} ipv4.gateway ${gateway} ipv4.method manual && sudo nmcli connection reload && sudo nmcli networking off && sleep 3 && sudo nmcli networking on`
			log.info('linux-设置本机网络命令行', cmd)
			exec(cmd)
		}
	},
	/**
	 * 重启终端
	 */
	rebootTerminal() {
		log.info('重启终端')
		if (process.platform == 'win32') {
			app.relaunch({ args: process.argv.slice(1).concat(['--relaunch']) })
			app.exit(0)
		} else {
			const terminalName = 'pym-client'
			exec(`kill -9 $(pgrep ${terminalName}) && cd /opt/gosuncn/${terminalName} && ./${terminalName}.sh`)
		}
		global.mainWin = null
	},
	/**
	 * 重启设备
	 */
	reboot() {
		if (process.platform === 'win32') {
			log.info('win32-系统重启命令', 'success')
			exec('shutdown -r -t 0')
		} else if (process.platform == 'linux') {
			log.info('linux-系统重启命令', 'success')
			log.info(process.arch, '平台架构')
			process.arch == 'x64' ? exec('shutdown -r now') : exec('sudo shutdown -r now')
		}
	},
	/**
	 * 获取属性
	 */
	getProperty(props = []) {
		return new Promise(async (resolve, reject) => {
			const getOtherInfo = () => {
				const version = actualVersion || app.getVersion() // 软件版本
				const { ip, mac } = this.getIpMAC()
				const deviceManage = `http://${ip}:9587` // 终端配置管理  9527
				return { version, ip, mac, deviceManage }
			}
			const getSiCpu = () => {
				return new Promise(async (res, rej) => {
					const cpu = await si.cpu()
					const threadNumber = cpu.cores // CPU核心线程数
					const cpuName = `${cpu.manufacturer} ${cpu.brand}` // cpu名称
					res({ threadNumber, cpuName })
				})
			}
			const getSiMem = () => {
				return new Promise(async (res, rej) => {
					const mem = await si.mem()
					const memory = (mem.total / 1024 / 1024).toFixed(2) // 总内存
					const free = (mem.free / 1024 / 1024).toFixed(2) // 空闲内存
					const used = (mem.used / 1024 / 1024).toFixed(2) // 使用内存
					res({ memory, free, used })
				})
			}
			const getSiOsInfo = () => {
				return new Promise(async (res, rej) => {
					const osInfo = await si.osInfo()
					const sysVersion = osInfo.distro // 系统版本
					const sysPlatform = osInfo.platform // 系统平台
					const sysArch = osInfo.arch // 系统架构
					res({ sysVersion, sysPlatform, sysArch })
				})
			}
			const getSiCurrentLoad = () => {
				return new Promise(async (res, rej) => {
					const currentLoad = await si.currentLoad()
					const cpuUsed = Math.floor(currentLoad.currentLoad) // cpu使用率
					res({ cpuUsed })
				})
			}

			if (props.length) {
				let obj = {}
				for (let i = 0; i < props.length; i++) {
					const prop = props[i]
					if (obj[prop]) {
						continue
					}
					if (['version', 'ip', 'mac', 'deviceManage'].includes(prop)) {
						Object.assign(obj, getOtherInfo())
					} else if (['cpuName', 'threadNumber'].includes(prop)) {
						const siCpu = await getSiCpu()
						Object.assign(obj, siCpu)
					} else if (['memory', 'free', 'used'].includes(prop)) {
						const siMem = await getSiMem()
						Object.assign(obj, siMem)
					} else if (['sysVersion', 'sysPlatform', 'sysArch'].includes(prop)) {
						const siOsInfo = await getSiOsInfo()
						Object.assign(obj, siOsInfo)
					} else if (['cpuUsed'].includes(prop)) {
						const siCurrentLoad = await getSiCurrentLoad()
						Object.assign(obj, siCurrentLoad)
					}
				}
				// 过滤出只需查询的数据
				let result = {}
				props.forEach((prop) => {
					result[prop] = obj[prop]
				})
				resolve(result)
			} else {
				const otherInfo = getOtherInfo()
				const siCpu = await getSiCpu()
				const siMem = await getSiMem()
				const siOsInfo = await getSiOsInfo()
				const siCurrentLoad = await getSiCurrentLoad()
				resolve({ ...otherInfo, ...siCpu, ...siMem, ...siOsInfo, ...siCurrentLoad })
			}
		})
	}
}
