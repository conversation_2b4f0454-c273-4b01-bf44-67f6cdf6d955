const path = require('path')
const fs = require('fs')
const request = require('request')
const Logger = require('../log/index')
const { ipcMain } = require('electron')
const tools = require('../mqtt/tools')

const log = new Logger('iot')
const isDevelopment = process.env.NODE_ENV !== 'production'

// 本地授权文件路径
const newAuthFilePath = path.join(isDevelopment ? process.cwd() : process.resourcesPath, 'extraResources/auth.lic')
// 本地mqtt配置文件路径
const newConfigFilePath = path.join(isDevelopment ? process.cwd() : process.resourcesPath, 'extraResources/serverConfig.json')

module.exports = {
	// 获取授权文件
	getAuthFile(fileUrl) {
		return new Promise((resolve, reject) => {
			log.info('授权文件下载地址fileUrl===', fileUrl)
			// 文件获取结果
			if (fileUrl) {
				// 下载并保存文件
				const stream = fs.createWriteStream(newAuthFilePath)
				request(encodeURI(fileUrl))
					.pipe(stream)
					.on('close', () => {
						resolve()
					})
			} else {
				log.info('获取授权文件error: 不存在fileUrl')
				reject('获取授权文件error: 不存在fileUrl')
			}
		})
	},

	// 授权文件解密
	decryptAuthFile() {
		const aesKey = 'gosun-device-iot'
		try {
			const authString = fs.readFileSync(newAuthFilePath).toString()
			log.info('授权文件地址: ', newAuthFilePath)
			log.info('读取授权文件结果: ', authString)
			global.iotWin.webContents.send('aes-decrypt', { authString, aesKey })
			ipcMain.on('aes-decrypt-result', (err, authResult) => {
				log.info('解密授权文件结果: ', authResult)
				return authResult
			})
		} catch (e) {
			log.error('授权文件解密失败: ', e)
			return {}
		}
	},

	// 授权信息比对: mac、 productKey
	authComparison(authObj) {
		const macAddress = tools.getMacAddress()
		const productKey = JSON.parse(fs.readFileSync(newConfigFilePath)).productKey
		const authObjParse = JSON.parse(authObj)
		log.info('比对: 读取macAddress: ', macAddress)
		log.info('比对: 读取productKey: ', productKey)
		log.info('比对: authObjParse: ', authObjParse)
		if (macAddress == authObjParse.mac && productKey == authObjParse.productKey) {
			return true
		}
		return false
	}
}
