const { app } = require('electron')
const { spawn, exec } = require('child_process')
const { EventEmitter } = require('events')
const fs = require('fs')
const path = require('path')
const request = require('request')
const Logger = require('../log/index')
const log = new Logger('upgrad')

const dirPath = process.platform === 'win32' ? 'C:\\pymClientTerminalPackage' : '/opt/gosuncn/pymClientTerminalPackage'
const isPackaged = app.isPackaged
const resourcesPath = isPackaged ? process.resourcesPath : process.cwd()

/**
 * 函数节流 - 每隔wait时间内只执行一次fn
 * @param {Function} fn 执行函数
 * @param {Number} wait 间隔时间
 */
const throttle = (fn, wait) => {
    let timer = null
    return function(...args) {
        if (timer) {
            return
        } else {
            timer = setTimeout(() => {
                fn.apply(this, args)
                clearTimeout(timer)
                timer = null
            }, wait)
        }
    }
}

class Updater extends EventEmitter {
    constructor(mainWindow) {
        super()
        this.mainWindow = mainWindow || null
        this.progressTimeout = 200 // 推送下载进度间隔(ms): 默认200ms
        this.totolSize = 0 // 升级安装包大小
        this.quitAndInstallCalled = false
    }

    download(downloadInfo) {
        const downloadUrl = downloadInfo.fileUrl || downloadInfo.url
        let dataBuffer = ''
        const postProgressFn = throttle(this.postProgress.bind(this), this.progressTimeout)

        Updater.createFolder()
        const fileName = downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1)
        const stream = fs.createWriteStream(path.join(dirPath, fileName))

        request
            .get(encodeURI(downloadUrl))
            .on('response', (res) => {
                log.info('安装包大小', res.headers['content-length'])
                this.totalSize = res.headers['content-length']
            })
            .on('data', (data) => {
                dataBuffer += data
                const progress = Updater.downloadProgress(dataBuffer.length, this.totalSize)
                postProgressFn(progress, downloadInfo)
            })
            .on('close', async() => {
                await this.downloadAfter()
                // 延迟执行：避免比 on data中的进度计算先执行
                setTimeout(() => {
                    this.postProgress(100, downloadInfo)
                    // 安装
                    this.install(fileName)
                }, this.progressTimeout)
            })
            .on('error', (error) => {
                this.emit('error', error)
            })
            .pipe(stream)
    }

    downloadAfter() {
        return new Promise((resolve) => {
            try {
                // 备份本地配置文件
                fs.copyFileSync(path.join(resourcesPath, 'extraResources/serverConfig.json'), path.join(dirPath, 'serverConfig.json'))
                fs.copyFileSync(path.join(resourcesPath, 'extraResources/auth.lic'), path.join(dirPath, 'auth.lic'))
                resolve(true)
            } catch (error) {
                log.error('升级备份文件失败：', error.toString())
                resolve(false)
            }
        })
    }

    // 安装
    install(fileName) {
        const installFilePath = path.join(dirPath, fileName)
        if (process.platform === 'win32') {
            log.info('开始安装，安装地址：' + installFilePath)
            fs.writeFileSync(app.getAppPath() + app.getName() + '.bat', 'choice /t 5 /d y /n \n' + 'start ' + installFilePath)
            app.once('quit', (e, exitCode) => {
                if (this.quitAndInstallCalled) {
                    log.info('更新安装程序已被触发。退出应用程序')
                    return
                }
                if (exitCode != 0) {
                    log.info('更新异常代码：' + exitCode)
                    return
                }
                this.winInstall(app.getAppPath() + app.getName() + '.bat')
            })
            const isInstall = this.winInstall(app.getAppPath() + app.getName() + '.bat')
            if (isInstall) {
                log.info('退出终端')
                setImmediate(() => {
                    log.info('检测进程删除', path.dirname(app.getPath('exe')))
                    log.info('32', resourcesPath)
                    spawn(path.join(resourcesPath, `/extraResources/delApp.bat`), [], {
                        detached: true,
                        stdio: 'ignore'
                    })
                }, 3000)
            } else {
                log.info('执行完成')
                this.quitAndInstallCalled = false
            }
        } else {
            const tipsTitle = '智能物品柜'
            const command = `
				#解压deb包
				notify-send -t 3000 '${tipsTitle}' '安装包正在解压，请耐心等待！'
				dpkg-deb -x ${installFilePath} ${dirPath}
				notify-send -t 3000 '${tipsTitle}' '安装包解压完成！'
				#移除没有赋权的文件
				rm -rf ${dirPath}/opt/gosuncn/pym-client/chrome-sandbox
				#复制并覆盖到原安装目录下
				cp -rf ${dirPath}/opt/gosuncn/pym-client /opt/gosuncn
				#检测进程 & 关闭进程
				kill -9 $(pgrep pym-client)
				#提示用户升级完成
				notify-send -t 3000 '${tipsTitle}' '升级完成！'
				#调起客户端
				cd /opt/gosuncn/pym-client && ./pym-client.sh
			`
            exec(command)
        }
    }
    winInstall(command) {
        if (this.quitAndInstallCalled) {
            log.info('安装中已触发')
            return false
        }
        this.quitAndInstallCalled = true
        log.info('触发安装命令')
        this._spawn(command, [])
        // exec(command)
        return true
    }
    _spawn(command, args) {
        return new Promise((resolve, reject) => {
            try {
                log.info('安装路径', command)
                const childProcess = spawn(command, args, {
                    detached: true,
                    stdio: 'ignore'
                })
                childProcess.on('error', (err) => {
                    log.info('错误执行返回：', err)
                    reject(err)
                })
                childProcess.unref()
                if (childProcess.pid !== undefined) {
                    log.info('成功执行返回')
                    resolve(true)
                }
            } catch (err) {
                reject(err)
            }
        })
    }
    // 上报进度
    postProgress(progress, oldDownloadInfo) {
        const progressNum = parseInt(progress)
        const downloadInfo = {
            ...oldDownloadInfo,
            progress: progressNum
        }
        this.emit('download-progress', downloadInfo)
    }

    // 进度计算
    static downloadProgress(cur, total) {
        return (cur / total).toFixed(2) * 100
    }

    // 创建文件夹
    static createFolder() {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath)
            log.info('文件夹创建成功!')
        } else {
            log.info('文件夹已存在!')
        }
    }
}

module.exports = Updater
