/**
@Author: lin<PERSON><PERSON><PERSON>
* @description: IOT 平台对接 鉴权窗口
* @Date: 2024-05-17 14:03:48
 * @Last Modified by: lin<PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-06-21 21:12:38
*/
const {
	BrowserWindow,
	ipcMain,
	app
} = require('electron')
const path = require('path')
const fs = require('fs')
const mqttTools = require('./client/mqtt/auth')
const Logger = require('./client/log/index')
const tools = require('./client/mqtt/tools')
const log = new Logger('iot')

// 旧配置文件路径
const packagePath = process.platform == 'win32' ? 'C:\\pymClientTerminalPackage' : '/opt/gosuncn/pymClientTerminalPackage'
const oldConfigFilePath = path.join(packagePath, 'serverConfig.json')
// 本地授权文件路径
const newAuthFilePath = path.join(!app.isPackaged ? process.cwd() : process.resourcesPath, 'extraResources/auth.lic')
// 旧授权文件路径
const oldAuthFilePath = path.join(packagePath, 'auth.lic')
let newResult = {} // 配置文件信息

const authWin = {
	win: null,
	async createWin(extraResources, appIcon) {
		ipcMain.on('start-auth', () => {
			log.info('on start auth...')
		})
		// await this.mergeConfig()

		this.win = new BrowserWindow({
			title: '附物管理终端',
			icon: appIcon,
			show: false,
			frame: false, // 无边框（窗口、工具栏等）
			width: 720,
			height: 480,
			webPreferences: {
				devTools: true,
				webSecurity: false,
				nodeIntegration: true,
				webgl: false,
				preload: path.join(extraResources, 'preload.js')
			},
			resizable: true,
			transparent: true // 窗口是否支持透明
		})
		try {
			const loadURL = app.isPackaged ? `file://${__dirname}/dist/index.html#/auth` : 'http://localhost:8840/#/auth'
			// const loadURL = app.isPackaged ? `file://${__dirname}/dist/index.html#/home` : 'http://localhost:8840/#/home'

			await this.win.loadURL(loadURL)
			newResult.alwaysOnTop && this.win.setAlwaysOnTop(true, 'pop-up-menu') // 窗口悬浮置于windows系统栏之上
			this.win.show()
			if (!app.isPackaged || newResult.openDev) {
				this.win.webContents.openDevTools()
			}
			// if (process.platform === 'linux') {
			// 	const bool = await this.networkVaild()
			// 	if (!bool) {
			// 		return Promise.resolve(false)
			// 	}
			// }
		} catch (e) {
			this.loadErrorPage()
			return Promise.resolve(false)
		}
		global.iotWin = this.win
		// 授权确认：授权比对正确进入主界面 否则退出应用
		return new Promise((resolve, reject) => {
			log.info('start auth...')
			this.auth().then(
				() => {
					log.info('Auth successful')
					// 跳转加载页面
					this.win.webContents.send('go-to-loading')
					resolve(true)
				},
				() => {
					resolve(false)
					// 跳转加载页面
					// this.win.webContents.send('go-to-loading')

					this.closeWin()
				}
			)
		})
	},

	networkVaild() {
		return new Promise(async (resolve, reject) => {
			// 应用启动检测ip是否配置，未配置则调起配置窗口
			const {
				ip
			} = tools.getIpMAC()
			if (ip) {
				resolve(true)
			} else {
				// 未配置IP信息，弹出配置窗口
				const bool = await this.setNetworkInfo()
				if (!bool) {
					this.closeWin()
					resolve(false)
				}
			}
		})
	},

	async loadErrorPage() {
		ipcMain.on('close-loading-error-dialog', (ev, arg) => {
			this.closeWin()
		})
		try {
			const localLoad = path.join(`file://${__dirname}/client/loadingError/index.html`)
			log.info('loading 本地地址：', localLoad)
			await this.win.loadURL(localLoad)
			this.win.show()
		} catch (error) {
			log.info('loading-error 初始化失败：', error.toString())
			this.closeWin()
			this.win = null
		}
	},

	// 授权文件 & IO配置文件处理
	mergeConfig() {
		return new Promise((resolve, reject) => {
			try {
				if (fs.existsSync(oldAuthFilePath)) {
					fs.copyFileSync(oldAuthFilePath, newAuthFilePath)
				} else {
					log.info('旧授权文件不存在, 无需复制')
				}
				newResult = JSON.parse(fs.readFileSync(global.terminalConfigFilePath) || '{}') || {}
				if (fs.existsSync(oldConfigFilePath)) {
					const oldResult = JSON.parse(fs.readFileSync(oldConfigFilePath) || '{}') || {}
					Object.assign(newResult, oldResult)
					log.info('合并后配置：', newResult)
					// 覆盖原配置文件
					fs.writeFileSync(global.terminalConfigFilePath, JSON.stringify(newResult, null, 2))
					// 删除升级安装包目录
					const state = fs.statSync(packagePath)
					if (state.isDirectory()) {
						fs.rmdirSync(packagePath, {
							recursive: true
						})
					}
				} else {
					log.info('旧配置文件不存在, 无需覆盖新配置文件')
				}
			} catch (error) {
				log.info('读取备份文件失败：', error.toString())
			} finally {
				resolve()
			}
		})
	},

	/**
	 * IP信息验证
	 */
	setNetworkInfo() {
		log.info('setNetworkInfo')
		this.win.webContents.send('open-config-window', {
			type: 'network'
		})
		return new Promise((resolve, reject) => {
			ipcMain.once('open-config-window-result', (event, result) => {
				if (result.type == 'confirm') {
					// 设置IP
					tools.setLocalNetwork(result.localIp, result.localDNS, result.localGetway, result.localNetmask)
					resolve(true)
				} else if (result.type == 'cancel') {
					// 未填写IP信息，关闭窗口，退出
					resolve(false)
				}
			})
		})
	},

	/**
	 * 授权文件验证
	 */
	auth() {
		// 后台没有授权文件下发，临时增加逻辑
		return new Promise((resolve, reject) => {
			// 没有MQTT相关配置则弹出配置窗口填写
			// if (!newResult.iotAddress || !newResult.productKey || !newResult.productName) {
			// 	this.win.webContents.send('open-config-window', { type: 'iot' })

			// 	ipcMain.once('open-config-window-result', (event, result) => {
			// 		log.info('MQTT配置窗口回传数据: ', result)
			// 		if (result.type == 'confirm' && result.data) {
			// 			// 写入本地配置文件
			// 			Object.assign(newResult, result.data)
			// 			fs.writeFileSync(global.terminalConfigFilePath, JSON.stringify(newResult, null, 2))
			// 		} else {
			// 			log.info('未填写MQTT配置!')
			// 		}
			// 		resolve(true)
			// 	})
			// } else {
			// 	resolve(true)
			// }
			resolve(true)
		})
		// 获取授权文件
		const toGetAuthFile = (resolveRoot, rejectRoot) => {
			return new Promise((resolve, reject) => {
				// 没有MQTT相关配置则弹出配置窗口填写
				if (!newResult.iotAddress || !newResult.productKey || !newResult.productName) {
					this.win.webContents.send('open-config-window', {
						type: 'iot'
					})

					ipcMain.once('open-config-window-result', (event, result) => {
						log.info('MQTT配置窗口回传数据: ', result)
						if (result.type == 'confirm' && result.data) {
							// 写入本地配置文件
							Object.assign(newResult, result.data)
							fs.writeFileSync(global.terminalConfigFilePath, JSON.stringify(newResult, null, 2))
							toGetAuthFile(resolve, reject)
						} else {
							reject('未填写MQTT配置!')
						}
					})
				} else {
					// 下载授权文件
					this.win.webContents.send('get-auth-file', {
						iotAddress: newResult.iotAddress,
						productKey: newResult.productKey
					})

					ipcMain.once('get-auth-file-result', (event, fileUrl) => {
						// 文件获取结果
						mqttTools
							.getAuthFile(fileUrl)
							.then(() => {
								const resolveFn = resolveRoot || resolve
								resolveFn()
							})
							.catch((err) => {
								const rejectFn = rejectRoot || reject
								rejectFn(err)
							})
					})
				}
			})
		}

		// 1、若存在授权文件，则解密文件再比对，比对异常，进行第2步；反之，直接进入首页
		// 2、是否配置IOT 平台地址 和 产品编码，没有配置则弹出配置窗口，再获取授权文件比对；反之则直接重新获取授权文件比对
		// 2.1、授权文件存入本地安装目录下
		// 2.2、比对异常，则关闭应用；反之，直接进入首页
		return new Promise((resolve, reject) => {
			const isExistAuthFile = fs.existsSync(newAuthFilePath)
			// 存在授权文件
			if (isExistAuthFile) {
				// 1、文件解密
				const authObj = mqttTools.decryptAuthFile()
				// 2、比对
				if (mqttTools.authComparison(authObj)) {
					// 比对成功， 正常启动应用
					resolve(true)
					return
				}
			}
			// 界面提示
			this.win.webContents.send('auth-file-error-tip', isExistAuthFile ? {
				message: '本地授权文件比对失败，正在下载新授权文件',
				type: 'comparison-auth-file-error'
			} : {
				message: '本地没有授权文件，正在下载授权文件',
				type: 'not-local-auth-file'
			})

			// 不存在授权文件 或者 比对失败第二次验证， 获取授权文件
			toGetAuthFile().then(
				() => {
					const authObj = mqttTools.decryptAuthFile()
					if (mqttTools.authComparison(authObj)) {
						// 进入界面
						resolve(true)
					} else {
						// 退出终端
						this.win.webContents.send('auth-file-error-tip', {
							message: '授权文件比对失败，将关闭终端',
							type: 'comparison-auth-file-error'
						})
						setTimeout(() => {
							reject(false)
						}, 2000)
					}
				},
				() => {
					reject()
				}
			)
		})
	},

	closeWin() {
		this.win.close()
	}
}
module.exports = authWin
