柜子 说明：
*****************************************************************
****************************对接说明*****************************
*****************************************************************

Electron  -> 人脸识别SDK: GoNc_FaceRecognition_Init 人脸初始化
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_Create 人脸识别器创建
实战平台  -> Electron: 下发图片通过 url 转成 bgr 格式
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_GetFaceFeature人脸特征提取
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_AddPerson 新增人员
Electron  -> Electron: 点击人脸登录
Electron  -> 人脸识别SDK: GXX_FaceRecognition_Init 初始化抓拍
Electron  -> 人脸识别SDK: GXX_FaceRecognition_CreateDetector 创建人脸抓拍器
人脸识别SDK  -> Electron: 返回抓拍图片
Electron  -> Electron: 转成 bgr 格式
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_GetFaceFeature 获取特征值
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_SearchPerson 通过特征值搜索人员
Electron -> 实战平台: 通过特征值搜索获取到的人员信息,并登录

*************************************************************************
*************************************************************************
*************************************************************************


运行web端：
npm run dev:web
运行客户端：
npm run dev:win


日志：/opt/gosuncn/pym-client

解压 覆盖 unzip -o xxx

下载向日葵远程电脑：
137 718 378 5  A12345   电脑密码：python
使用xshell  访问服务：
**************   / 123456

代码在 /home/<USER>/Desktop
打包命令：
npm run update-version && npm run build:web && npm run build:linux-amd64-deb
打包完成后 在/home/<USER>/Desktop/code/x64_linux文件夹下
拷出打包文件安装在桌面柜子
访问服务： ************ 账号密码  GXX/123456
右键设置权限777
柜子包安装命令：
sudo dpkg -i  xxxx    比如： pym-client_amd64_5.0.3.4.2025.07.15.1954.deb




