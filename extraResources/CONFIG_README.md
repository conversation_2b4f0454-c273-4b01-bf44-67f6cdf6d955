# serverConfig文件说明

# alwaysOnTop：Boolean 窗口是否永远在别的窗口的上面
# appName：String
# baudRate：Number 锁控 baudRate
# bspFaceLoginCode：String
# cabinetCode：String
# cabinetConfig：String
# cabinetPwd：String
# centerId：String
# clientName：String
# clientPwd：String
# copyright：String
# faceCamera：String 人脸摄像头名称
# fullscreen：Boolean 是否全屏
# horizontal：String 是否水平翻转，默认N：不翻转，Y：翻转
# iotAddress：String
# isFrame：Boolean 是否设置无边窗口
# isHaiJing：Boolean
# lightBaudRate：Number 补光灯 baudRate
# lightCom：String 补光灯 COM
# lightControlProtocol：Number 补光灯协议 1：ws 2：com
# lightType：Number
# lockCom：String 锁控 COM
# lockControlProtocol：Number 补光灯协议 1：ws 2：com
# lockPlateStartIndex：Number 锁板起始值
# lockType：Number
# logSaveDay：Number 日志保存时间（单位/天）
# loginOutTime：Number
# manufacturer：Number 终端机供应商 - kuan、jijia、dichuan
# openDev：Boolean 是否开启终端调试工具
# openFillLight：String
# originUrl：String
# patternType：Number 柜子模式：保管（0）、暂存（1），物管配置要是没有返回则从终端配置读取
# photoshopMode：Number 人脸算法供应商 - 0：没有配置人脸、1：高新兴
# policeName：String
# productKey：String
# productName：String
# remoteHttpServerProt：Number 终端外设服务HTTP端口
# remoteWsServerProt：Number 终端外设服务WS端口
# resizable：Boolean 终端是否允许自定义大小
# rotate：Number 是否旋转90度，默认N:不旋转，Y旋转
# server：String 静态资源服务地址
# serverVersion：String
# setMenu：Boolean 是否设置菜单
# terminal：String
# transparent：Boolean 透明背景
# version：String 版本号
# webSocketFillLight：String
# webSocketLocket：String
# winHeight：Number 终端高度
# winWidth：Number 终端宽度
