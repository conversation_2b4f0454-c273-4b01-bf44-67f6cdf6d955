7767517
75 85
Input                    input                    0 1 input
Convolution              Conv_0                   1 1 input 425 0=32 1=3 3=2 4=1 5=1 6=864 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_5                   1 1 425 430 0=32 1=3 4=1 5=1 6=288 7=32 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_10                  1 1 430 432 0=16 1=1 5=1 6=512
Convolution              Conv_12                  1 1 432 437 0=96 1=1 5=1 6=1536 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_17                  1 1 437 442 0=96 1=3 3=2 4=1 5=1 6=864 7=96 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_22                  1 1 442 444 0=24 1=1 5=1 6=2304
Split                    splitncnn_0              1 2 444 444_splitncnn_0 444_splitncnn_1
Convolution              Conv_24                  1 1 444_splitncnn_1 449 0=144 1=1 5=1 6=3456 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_29                  1 1 449 454 0=144 1=3 4=1 5=1 6=1296 7=144 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_34                  1 1 454 456 0=24 1=1 5=1 6=3456
BinaryOp                 Add_36                   2 1 444_splitncnn_0 456 457
Convolution              Conv_37                  1 1 457 462 0=144 1=1 5=1 6=3456 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_42                  1 1 462 467 0=144 1=3 3=2 4=1 5=1 6=1296 7=144 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_47                  1 1 467 469 0=32 1=1 5=1 6=4608
Split                    splitncnn_1              1 2 469 469_splitncnn_0 469_splitncnn_1
Convolution              Conv_49                  1 1 469_splitncnn_1 474 0=192 1=1 5=1 6=6144 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_54                  1 1 474 479 0=192 1=3 4=1 5=1 6=1728 7=192 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_59                  1 1 479 481 0=32 1=1 5=1 6=6144
BinaryOp                 Add_61                   2 1 469_splitncnn_0 481 482
Split                    splitncnn_2              1 2 482 482_splitncnn_0 482_splitncnn_1
Convolution              Conv_62                  1 1 482_splitncnn_1 487 0=192 1=1 5=1 6=6144 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_67                  1 1 487 492 0=192 1=3 4=1 5=1 6=1728 7=192 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_72                  1 1 492 494 0=32 1=1 5=1 6=6144
BinaryOp                 Add_74                   2 1 482_splitncnn_0 494 495
Convolution              Conv_75                  1 1 495 500 0=192 1=1 5=1 6=6144 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_80                  1 1 500 505 0=192 1=3 3=2 4=1 5=1 6=1728 7=192 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_85                  1 1 505 507 0=64 1=1 5=1 6=12288
Split                    splitncnn_3              1 2 507 507_splitncnn_0 507_splitncnn_1
Convolution              Conv_87                  1 1 507_splitncnn_1 512 0=384 1=1 5=1 6=24576 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_92                  1 1 512 517 0=384 1=3 4=1 5=1 6=3456 7=384 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_97                  1 1 517 519 0=64 1=1 5=1 6=24576
BinaryOp                 Add_99                   2 1 507_splitncnn_0 519 520
Split                    splitncnn_4              1 2 520 520_splitncnn_0 520_splitncnn_1
Convolution              Conv_100                 1 1 520_splitncnn_1 525 0=384 1=1 5=1 6=24576 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_105                 1 1 525 530 0=384 1=3 4=1 5=1 6=3456 7=384 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_110                 1 1 530 532 0=64 1=1 5=1 6=24576
BinaryOp                 Add_112                  2 1 520_splitncnn_0 532 533
Split                    splitncnn_5              1 2 533 533_splitncnn_0 533_splitncnn_1
Convolution              Conv_113                 1 1 533_splitncnn_1 538 0=384 1=1 5=1 6=24576 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_118                 1 1 538 543 0=384 1=3 4=1 5=1 6=3456 7=384 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_123                 1 1 543 545 0=64 1=1 5=1 6=24576
BinaryOp                 Add_125                  2 1 533_splitncnn_0 545 546
Convolution              Conv_126                 1 1 546 551 0=384 1=1 5=1 6=24576 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_131                 1 1 551 556 0=384 1=3 4=1 5=1 6=3456 7=384 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_136                 1 1 556 558 0=96 1=1 5=1 6=36864
Split                    splitncnn_6              1 2 558 558_splitncnn_0 558_splitncnn_1
Convolution              Conv_138                 1 1 558_splitncnn_1 563 0=576 1=1 5=1 6=55296 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_143                 1 1 563 568 0=576 1=3 4=1 5=1 6=5184 7=576 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_148                 1 1 568 570 0=96 1=1 5=1 6=55296
BinaryOp                 Add_150                  2 1 558_splitncnn_0 570 571
Split                    splitncnn_7              1 2 571 571_splitncnn_0 571_splitncnn_1
Convolution              Conv_151                 1 1 571_splitncnn_1 576 0=576 1=1 5=1 6=55296 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_156                 1 1 576 581 0=576 1=3 4=1 5=1 6=5184 7=576 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_161                 1 1 581 583 0=96 1=1 5=1 6=55296
BinaryOp                 Add_163                  2 1 571_splitncnn_0 583 584
Convolution              Conv_164                 1 1 584 589 0=576 1=1 5=1 6=55296 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_169                 1 1 589 594 0=576 1=3 3=2 4=1 5=1 6=5184 7=576 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_174                 1 1 594 596 0=160 1=1 5=1 6=92160
Split                    splitncnn_8              1 2 596 596_splitncnn_0 596_splitncnn_1
Convolution              Conv_176                 1 1 596_splitncnn_1 601 0=960 1=1 5=1 6=153600 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_181                 1 1 601 606 0=960 1=3 4=1 5=1 6=8640 7=960 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_186                 1 1 606 608 0=160 1=1 5=1 6=153600
BinaryOp                 Add_188                  2 1 596_splitncnn_0 608 609
Split                    splitncnn_9              1 2 609 609_splitncnn_0 609_splitncnn_1
Convolution              Conv_189                 1 1 609_splitncnn_1 614 0=960 1=1 5=1 6=153600 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_194                 1 1 614 619 0=960 1=3 4=1 5=1 6=8640 7=960 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_199                 1 1 619 621 0=160 1=1 5=1 6=153600
BinaryOp                 Add_201                  2 1 609_splitncnn_0 621 622
Convolution              Conv_202                 1 1 622 627 0=960 1=1 5=1 6=153600 9=3 -23310=2,0.000000e+00,6.000000e+00
ConvolutionDepthWise     Conv_207                 1 1 627 632 0=960 1=3 4=1 5=1 6=8640 7=960 9=3 -23310=2,0.000000e+00,6.000000e+00
Convolution              Conv_212                 1 1 632 634 0=320 1=1 5=1 6=307200
Convolution              Conv_214                 1 1 634 639 0=1280 1=1 5=1 6=409600 9=3 -23310=2,0.000000e+00,6.000000e+00
Reduction                ReduceMean_219           1 1 639 640 0=3 1=0 -23303=2,1,2 5=1
InnerProduct             Gemm_220                 1 1 640 output 0=2 1=1 2=2560
