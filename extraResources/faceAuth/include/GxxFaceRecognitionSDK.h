#ifndef _GXX_FACE_RECOGNITION_SDK_H 
#define _GXX_FACE_RECOGNITION_SDK_H

#ifndef SVTCLIENT_EXTERN_C
#ifdef __cplusplus
#define SVTCLIENT_EXTERN_C extern "C"
#else
#define SVTCLIENT_EXTERN_C
#endif
#endif


#if (defined WIN32 || defined _WIN32 || defined WIN64 || defined _WIN64) && (defined _MSC_VER)
#define SVTCLIENT_STDCALL __stdcall
#define SVTCLIENT_API_EXPORTS  __declspec(dllimport)

#else
#define SVTCLIENT_STDCALL
#define SVTCLIENT_API_EXPORTS 
#endif

#define SVTCLIENT_IMPL SVTCLIENT_EXTERN_C

#define SVTCLIENT_API(rettype) SVTCLIENT_EXTERN_C SVTCLIENT_API_EXPORTS rettype SVTCLIENT_STDCALL

#define NUM_THREADS 4
#define FR_POINT_LEN 5


//��������ֵ
enum EN_FRErrorCode {
	FR_SDK_SUCCEED = 0,         //�ɹ�
	FR_SDK_NOT_INIT = 1,        //δ��ʼ��
	FR_SDK_UNKNOWN_ERROR = 2,   //δ֪����
	FR_SDK_INVAILD_PARAM,       //�����������
	FR_SDK_UNREGISTERED,        //����ʶ���㷨��δע��
	FR_SDK_UNREGISTERED_ATTRI,  //��������ʶ���㷨��δע��
	FR_SDK_NO_EXIST,            //������
	FR_SDK_OUTOF_RES,           //ϵͳ��Դ����
	FR_SDK_TOO_SMALL,			//�����������С
};


//�������͵ĵ�����
typedef struct _FR_POINTF//
{
	float x;
	float y;
}FR_POINTF;

//��������
typedef struct _FR_RECT
{
	int x;
	int y;
	int width;
	int height;
}FR_RECT;
typedef struct _FR_RECT2F
{
	float x;
	float y;
	float width;
	float height;
}FR_RECT2F;

//������̬
typedef struct _FR_FACE_POSE {
	// ���ҽǶ�, ȡֵ��Χ[-60,60]. �Ҳ�Ƕ�Ϊ��, ���Ƕ�Ϊ�� ,��������0��
	float horizontalAngle;    //����ˮƽƫת�ǣ�����Ϊ������Ϊ����
							  // �����Ƕ�, ȡֵ��Χ[-15,15]. ̧ͷ�Ƕ�Ϊ��, ��ͷ�Ƕ�Ϊ��, ��������0��
	float verticalAngle;      //������ֱƫת�ǣ�����Ϊ������Ϊ����
} FR_FACE_POSE;

//����������
typedef struct _FR_DETE_PARAM {
	int x;                        //��������������ϽǺ�����
	int y;                        //��������������Ͻ�������
	int width;                    //�������������
	int height;                   //�����������߶�
	int minFace;                  //��С����
	int maxFace;                  //�������
	FR_FACE_POSE poseThresold;    //��̬������ֵ�����ҽǶ�, ȡֵ��Χ[-60,60]�������Ƕ�, ȡֵ��Χ[-15,15]��
	int AmbiguitThresold;         //Ĭ��60������ͼ�������ĺ���������Խ��Խ�ã�
	int streamType;               //�����ͣ�0����Ƶ����1��ͼƬ��
} FR_DETE_PARAM;

//��������ʵ������ֵ
typedef struct _FR_TRACK_RECT {
	int x;                                     //�����������ϽǺ�����
	int y;                                     //�����������Ͻ�������
	int width;                                 //����������
	int height;                                //��������߶�
	int ID;                                    //��������ID
	FR_POINTF faceFeaturePoints[FR_POINT_LEN]; //���������㼯
} FR_TRACK_RECT;


#ifdef __cplusplus
extern "C" {
#endif

	/*****************************
	* @brief   ��ʼ����������㷨��
	* @param   const char * licPath  --license·��
	* @param   int maxIDs        --��֧�����ʹ��·������CreateDetector��
	* @return  int      --�ɹ�����FR_SDK_SUCCEED,ʧ�ܷ�������������
	******************************/
	SVTCLIENT_API(int)	        GXX_FaceRecognition_Init(const char* licPath, int maxIDs);
	/*****************************
	* @brief   �����������㷨��
	* @param   void
	* @return  void
	******************************/
	SVTCLIENT_API(void)	        GXX_FaceRecognition_CleanUp(void);
	/****************************************
	* @brief   ������������������ؾ��ID�������������Դ�ID���в���
	* @szModel_path ģ���ļ�·��
	* @param   int* detectorID          --�ɹ���ֵ����õ���id
	* @return  int       --�ɹ�����FR_SDK_SUCCEED,ʧ�ܷ�������������
	****************************************/
	SVTCLIENT_API(int)	        GXX_FaceRecognition_CreateDetector(const char*  szModel_path, int*detectorID);
	/****************************************
	* @brief   �ͷ�����������������ڴ������������ʹ��
	* @param   int detectorID --����ʶ����ID
	* @return  void
	****************************************/
	SVTCLIENT_API(void)        GXX_FaceRecognition_ReleaseDetector(int detectorID);
	/****************************************
	* @brief   ��һ֡���ݼ������λ��
	* @param   int detectorID             --����ʶ����ID
	* @param   const char * imgBuf        --֡����ͷָ��,BGR��ʽ
	* @param   int width                  --֡���
	* @param   int height                 --֡�߶�
	* @param   const FR_DETE_PARAM * deteParam --����������������Ϊ��
	* @param   FD_TRACK_RECT ** facesRC   --���ص�������������(ֻ�����������޸�)
	* @param   int* faceCount             --���ص������������
	* @return  int      --�ɹ�����FR_SDK_SUCCEED,ʧ�ܷ�������������
	****************************************/
	SVTCLIENT_API(int)  GXX_FaceRecognition_Detect(int detectorID, const char* imgBuf, int width, int height, const FR_DETE_PARAM* deteRC, FR_TRACK_RECT** facesRC, int* faceCount);


#ifdef __cplusplus
}
#endif





#endif // _GXX_FACE_RECOGNITION_SDK_H