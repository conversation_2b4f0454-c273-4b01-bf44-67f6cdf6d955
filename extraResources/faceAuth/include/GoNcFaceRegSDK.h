/***************************************
* @file     GoNcFaceRegSDK.h
* @brief    人脸识别算法库
* @details
* <AUTHOR> @date     2018-9
* @mod      add  android log  ang yuv420(nv21) support
****************************************/
#ifndef GXX_FACE_RECOGNITION_SDK_H
#define GXX_FACE_RECOGNITION_SDK_H

#ifndef SVTCLIENT_EXTERN_C
#ifdef __cplusplus
#define SVTCLIENT_EXTERN_C extern "C"
#else
#define SVTCLIENT_EXTERN_C
#endif
#endif

#define SVTCLIENT_API_EXPORTS /*__declspec(dllimport)*/

#ifndef SVTCLIENT_STDCALL
#if (defined WIN32 || defined _WIN32 || defined WIN64 || defined _WIN64) && (defined _MSC_VER)
#define SVTCLIENT_STDCALL __stdcall
#else
#define SVTCLIENT_STDCALL
#endif
#endif

#define SVTCLIENT_IMPL SVTCLIENT_EXTERN_C

#define SVTCLIENT_API(rettype) SVTCLIENT_EXTERN_C SVTCLIENT_API_EXPORTS rettype SVTCLIENT_STDCALL

#define FR_POINT_NUM 5

#define  FACE_REG_SDK_VERSION "4.2.1.20250729"
//函数返回值
enum GoNcFRErrorCode {
    GO_NC_FR_SUCCEED = 0,         //成功
    GO_NC_FR_NOT_INIT = 1,        //未初始化
    GO_NC_FR_UNKNOWN_ERROR = 2,   //未知错误
    GO_NC_FR_INVAILD_PARAM,       //输入参数有误
    GO_NC_FR_UNREGISTERED,        //人脸识别算法库未注册
    GO_NC_FR_ID_NOT_EXIST,        //ID不存在
    GO_NC_FR_OUTOF_RES,           //系统资源不足
    GO_NC_FR_ALIGN_FAILED,        //人脸对齐失败
    GO_NC_FR_DET_FAILED,		  //人脸检测失败
    GO_NC_FR_FEA_EXT_FAILED,      //人脸特征提取失败
    GO_NC_FR_RE_REGISTER,         //重复注册
    GO_NC_PERSON_NOT_EXIST        //人员不存在 
    
};

//浮点类型的点坐标
typedef struct _GoPoint2f//
{
    float fX;
    float fY;
}GoPoint2f;

//人脸姿态
typedef struct _FacePose{
    float fHorizontal_threshold;    //人脸水平偏转程度限制，范围是0到1，越大则限制越小，1为不过滤。
} FacePose;

//人脸检测参数
typedef struct _FaceDetectFilterParams {
    int nX;                         //人脸检测区域左上角横坐标
    int nY;                         //人脸检测区域左上角纵坐标
    int nWidth;                     //人脸检测区域宽度
    int nHeight;                    //人脸检测区域高度
    int nMin_size;                  //最小人脸
    int nMax_size;                  //最大人脸
    FacePose pose_thresold;		   //姿态估计阈值
    int nStream_type;               //流类型：0表示视频流(带跟踪id号)，1表示图片流
} FaceDetectFilterParams;

//人脸区域，实际坐标值
typedef struct _FaceTrackedRect {
    int nX;                                     //人脸区域左上角横坐标
    int nY;                                     //人脸区域左上角纵坐标
    int nWidth;                                 //人脸区域宽度
    int nHeight;                                //人脸区域高度
    int nID;                                    //人脸跟踪ID
    GoPoint2f face_points[FR_POINT_NUM];	   //人脸特征点集
} FaceTrackedRect;

//人员信息，注册库
typedef struct _PersonInfo {
    int person_id_;                     //人员ID,唯一
    signed char face_feature_[512];     //人脸特征
} PersonInfo;

//检索信息
typedef struct _PersonSearchInfo {
    int person_id_;                     //人员ID,唯一
    float similarity_;                  //相似度
} PersonSearchInfo;


enum  GoNcFaceAction{
    GoNC_SHAKEHEAD=0,    // 摇头
    GoNC_BLINK,        // 眨眼
    GoNC_OPENMOUTH     // 张嘴
};

enum  GoNcFaceOcclusion{
    GoNC_OCCLUSION=0,    // 遮挡
    GoNC_NO_OCCLUSION=1       //非遮挡
};

typedef void(*GoNc_AntiSpoofingCB)(FaceTrackedRect* livingFaceRect,int rectNum,long long timestamp,int errcode);

#ifdef __cplusplus
extern "C" {
#endif
// 得到最近一次的错误码
SVTCLIENT_API(int)          GoNc_FaceRecognition_GetLastErrorCode(void);

// 初始化
SVTCLIENT_API(int)	        GoNc_FaceRecognition_Init(const char* license_file);
// 反初始化
SVTCLIENT_API(int)	        GoNc_FaceRecognition_CleanUp(void);

// 创建人脸识别实例
SVTCLIENT_API(int)	        GoNc_FaceRecognition_Create(const char* szModel_path,long long *phHandle);
// 释放人脸识别实例
SVTCLIENT_API(int)          GoNc_FaceRecognition_Release(const long long hHandle);

//YUV-NV21转换为BGR图像
//todo
SVTCLIENT_API(int)          GoNc_FaceRecognition_Nv21Tbgr(const long long hHandle, unsigned char *szImgYuv, unsigned char *szImgBgr,int nWidth, int nHeight );

// 对一帧数据检测人脸位置
// hHandle		         入参：实例的句柄
// szImg_buf		     入参：bgr图像数据
// nWidth
// nHeight
// pDet_filter_params    入参：过滤参数，具体看上面结构体定义
// nNum_threads          入参：运行该算法调用的线程数量（在合理范围内，数值越大则cpu占用越高，速度越快）
// nImg_type             入参：图像数据的类型
// ppFace_rc_arr         出参：检测结果信息
// pnFace_count          出参：人脸个数
SVTCLIENT_API(int)          GoNc_FaceRecognition_FaceDetect(const long long hHandle, const unsigned char* szImg_buf, int nWidth, int nHeight,
                                                            const FaceDetectFilterParams* pDet_filter_params, int nNum_threads, FaceTrackedRect** ppFace_rc_arr, int* pnFace_count);

//单帧活体检测
// hHandle		         入参：实例的句柄
// szImg_buf		     入参：bgr图像数据
// nWidth                入参：图像的宽度
// nHeight               入参: 图像的高度
// FacesRC               入参：人脸检出框及其关键点信息结构体
// nNum_threads          入参：运行该算法调用的线程数量（在合理范围内，数值越大则cpu占用越高，速度越快）
// asResult              出参：活体识别结果输出，0代表活体，1代表假体
SVTCLIENT_API(int)          GoNc_FaceRecognition_AntiSpoofingProcess(long long hHandle,
                                                        const unsigned char* szImg_buf,
                                                        int nWidth, int nHeight,
                                                        FaceTrackedRect* FacesRC, int nNum_threads, int* asResult);



//双目单帧活体检测(bgr图像+红外灰度图像)
// hHandle		         入参：实例的句柄
// szImg_buf		     入参：bgr图像数据
// nWidth                入参：bgr图像的宽度
// nHeight               入参: bgr图像的高度
// FacesRC               入参：人脸检出框及其关键点信息结构体
// szImg_buf_ir		  入参：红外图像数据(灰度)
// nWidth_ir             入参：红外图像的宽度
// nHeight_ir            入参: 红外图像的高度
// nNum_threads   入参：运行该算法调用的线程数量（在合理范围内，数值越大则cpu占用越高，速度越快）
// asResult              出参：活体识别结果输出，0代表活体，1代表假体
SVTCLIENT_API(int)  GoNc_FaceRecognition_AntiSpoofingProcess_DoubleSensor(long long hHandle, const unsigned char* szImg_buf, int nWidth, int nHeight, FaceTrackedRect* FacesRC,
	const unsigned char* szImg_buf_ir, int nWidth_ir, int nHeight_ir, const FaceDetectFilterParams* pDet_filter_params_ir,int nNum_threads, int* asResult);


// 提取人脸特征
// hHandle		          入参：实例的句柄
// szImg_buf		      入参：bgr图像数据
// nWidth
// nHeight
// pFace_rc               入参：输入人脸信息
// nNum_threads           入参：运行该算法调用的线程数量（在合理范围内，数值越大则cpu占用越高，速度越快）
// ppFeature              出参：人脸特征
// pnFeature_len          出参：人脸特征长度
SVTCLIENT_API(int)          GoNc_FaceRecognition_GetFaceFeature(const long long hHandle, const unsigned char* szImg_buf, int nWidth, int nHeight, const FaceTrackedRect* pFace_rc, int nNum_threads, signed char** ppFeature, int* pnFeature_len);

// 对2个人脸特征数据进行比对，得到相似度0-1
// hHandle		     入参：实例的句柄
// szFeature1         入参：取自GoNc_FaceRecognition_GetFaceFeature的特征向量
// npFeature_len1      入参：特征长度
// szFeature2         入参：取自GoNc_FaceRecognition_GetFaceFeature的特征向量
// npFeature_len2      入参：特征长度
// fpSimilarity        出参：相似度
SVTCLIENT_API(int)          GoNc_FaceRecognition_CompareFeature(const long long hHandle, const signed char* szFeature1, const int* npFeature_len1, const signed char* szFeature2, const int* npFeature_len2, float* fpSimilarity);

///////注册库管理与1:N检索:

//人员注册库导入
// hHandle		     入参：实例的句柄
// persons_          入参：人员信息库（数组的形态），单个人信息见PersonInfo，主要包含id和特征
// person_num_       入参：人员数
SVTCLIENT_API(int) GoNc_FaceRecognition_ImportPersons(long long hHandle, PersonInfo *persons_,int person_num_);

//清空人员注册库
// hHandle		     入参：实例的句柄
SVTCLIENT_API(void) GoNc_FaceRecognition_ClearPersons(long long hHandle);

//添加人员
// hHandle		     入参：实例的句柄
// person_           入参：入库的人员信息，单个人信息见PersonInfo，主要包含id和特征
// rep_thresold_     入参：重复注册判断阈值，当注册库中存在与输入人员特征相似都超过判断阈值的，则视为重复注册，将不入库
SVTCLIENT_API(int) GoNc_FaceRecognition_AddPerson(long long hHandle, PersonInfo person_,float rep_thresold_);

//删除人员
// hHandle		     入参：实例的句柄
// person_id_        入参：要求删除的人员id
SVTCLIENT_API(int) GoNc_FaceRecognition_DeletePerson(long long hHandle, int person_id_);

//更新指定人员特征
// hHandle		     入参：实例的句柄
// person_           入参：根据id更新对应人员的特征
SVTCLIENT_API(int) GoNc_FaceRecognition_UpdatePersonFeature(long long hHandle, PersonInfo person_);

//1:N检索
// hHandle		     入参：实例的句柄
// feature_          入参：用于检索的特征向量地址
// feature_len_      入参：用于检索的特征向量长度
// thresold_		 入参：阈值，大于该阈值的人员才可能被输出
// max_num_          入参：topN，最多输出大于设定阈值的人员数
// searched_result_  出参：检索结果，数组，每个成员定义见PersonSearchInfo（主要是id和相似度）,按照相似度从高到低排序
// result_num_       出参：检索出的人数
SVTCLIENT_API(int) GoNc_FaceRecognition_SearchPerson(long long hHandle, const signed char* feature_, int feature_len_,float thresold_,int max_num_,
                                        PersonSearchInfo **searched_result_,int *result_num_);

// hHandle		     入参：实例的句柄
// type              入参：人脸动作类型
SVTCLIENT_API(int) GoNc_FaceRecognition_AntiSpoofing_SetFaceActionType(long long hHandle,GoNcFaceAction type);

// hHandle		     入参：实例的句柄
// szImg_buf		 入参：bgr图像数据
// nWidth            入参：bgr图像的宽度
// nHeight           入参: bgr图像的高度
// timestamp         入参数：图像时间戳(14位)
SVTCLIENT_API(int) GoNc_FaceRecognition_AntiSpoofing_PushFrame(long long hHandle,const unsigned char* szImg_buf,int nWidth, int nHeight,long long timestamp);


// hHandle		     入参：实例的句柄
// cb                入参：回调函数
SVTCLIENT_API(int) GoNc_FaceRecognition_AntiSpoofing_SetCB(long long hHandle,GoNc_AntiSpoofingCB cb);

SVTCLIENT_API(int)  GoNc_FaceRecognition_OcclusionFace(long long hHandle, const unsigned char* szImg_buf, int nWidth, int nHeight, FaceTrackedRect FacesRC,int nNum_threads,GoNcFaceOcclusion* occlusionResult);


#ifdef __cplusplus
}
#endif

#endif //GXX_FACE_RECOGNITION_SDK_H
