const { ipc<PERSON><PERSON><PERSON>, contextBridge } = require('electron')
const { exec } = require('child_process')

ipcRenderer.on('isClose', (event, arg) => {
	localStorage.removeItem('userInfo')
	localStorage.removeItem('serverConfig')
	localStorage.removeItem('facereturn')
})
contextBridge.exposeInMainWorld('electronAPI', {
	node: () => process.versions.node,
	chrome: () => process.versions.chrome,
	electron: () => process.versions.electron,
	isMainWindow: 1,
	platform: process.platform,
	ipcRenderer: { ...ipcRenderer, on: ipcRenderer.on.bind(ipcRenderer), once: ipcRenderer.once.bind(ipcRenderer), removeAllListeners: ipcRenderer.removeAllListeners.bind(ipc<PERSON>enderer) }
})
contextBridge.exposeInMainWorld('nodeAPI', {
	exec
})
