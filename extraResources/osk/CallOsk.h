// ���� ifdef ���Ǵ���ʹ�� DLL �������򵥵�
// ��ı�׼�������� DLL �е������ļ��������������϶���� CALLOSK_EXPORTS
// ���ű���ġ���ʹ�ô� DLL ��
// �κ�������Ŀ�ϲ�Ӧ����˷��š�������Դ�ļ��а������ļ����κ�������Ŀ���Ὣ
// CALLOSK_API ������Ϊ�Ǵ� DLL ����ģ����� DLL ���ô˺궨���
// ������Ϊ�Ǳ������ġ�
#ifdef CALLOSK_EXPORTS
#define CALLOSK_API __declspec(dllexport)
#else
#define CALLOSK_API __declspec(dllimport)
#endif

// ����C:\Windows\System32\osk.exe������CreateProcess�Ĵ����룬0 �ǳɹ�
CALLOSK_API int CallOsk();
