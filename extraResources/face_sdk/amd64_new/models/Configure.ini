## configure file version
version=2.0

## Parameter: input size for face detection. 
## Parameter Constraints: [int]
FaceDetect_size=320

## Parameter: FaceDetect Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceDetectModelName=FaceDetector_Yunet320_int8_20231115

FaceDetectModelName_IR=RetinaFace_ir_mnet25_Final_fp32

## Parameter: threshold for face detect 
## Parameter Constraints: [float], Ranges: [0, 1]
detect_threshold=0.45
detect_threshold_IR=0.5

## Parameter: open antispoofingdetect or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing=1

## Parameter: threshold for antispoofing detection. 
## Parameter Constraints: [float], Ranges: 0.0 <= detection_thresh <= 1.0. High confidence, more faces are non-liveFace
antispoofing_threshold=0.55

## Parameter: AntiSpoofing Model Name. 
AntiSpoofingModelName=mobileNet_AS_fp32_20231026
## Parameter: AntiSpoofing Model input size. 
AntiSpoofing_width=154
AntiSpoofing_height=154


## Parameter: open AntiSpoofing_IR or not
## Parameter Constraints: [int], 0 or 1
is_open_antispoofing_IR=1 

## Parameter: AntiSpoofing_IR Model Name. 
AntiSpoofingModelName_IR=mobileNetv2_AS_IR_fp32_20250729
antispoofing_threshold_IR=0.55
antispoofing_threshold_IOU=-1.0



## Parameter: FaceReg Model Name. 
## Parameter Constraints: [string] just recording and try to not work
FaceRegModelName=GoFaceReco_Airface_L106_V4_20190929_nobn_fp32


## Parameter: use default models params. 
## Parameter Constraints: [int], 0 or 1
UseDefault=0

## Parameter: use default scale width. 
## Parameter Constraints: [int]
scale_w=640


## Parameter: use default scale height. 
## Parameter Constraints: [int]
scale_h=360

#张嘴阈值(小于该阈值为张嘴)
openMouthThres=0.55
#保持张嘴状态帧数阈值
openMouthKeepTimesThres=3
#闭嘴阈值(小于该阈值为闭嘴)
closeMouthThres=0.45
#保持闭嘴状态帧数阈值
closeMouthKeepTimesThres=3
#嘴巴EAR变化次数
mouthEARVarTimesThres=5
#闭眼阈值
closeEyeThres=0.17
#闭眼状态帧数阈值
closeEyeTimesThres=1
#眼睛EAR变化次数
eyeEARVarTimesThres=5
#摇头阈值
shakeHeadThres=0.2
#摇头过程帧数阈值
shakeHeadTimesThres=3
#配合式活体检测间隔(每n帧检测1次)
coordinateAntiSpoofDetInterval=3

FaceOcclusionModelName=OcclustionFace_mobilev2_fp32_20250605
FaceOcclusionThres=0.6

savepic=0
