<template>
	<div class="layouts">
		<Header @setCollapse="setCollapse"></Header>
		<div class="main">
			<LeftMenu :isCollapse="isCollapse"></LeftMenu>
			<div class="box">
				<router-view></router-view>
			</div>
		</div>
	</div>
</template>

<script>
import Header from '@/web/pages/layouts/components/head'

import LeftMenu from '@/web/pages/layouts/components/left-menu'
export default {
	name: 'Layouts',
	components: {
		Header,
		LeftMenu
	},
	data() {
		return {
			isCollapse: false
		}
	},
	methods: {
		setCollapse() {
			this.isCollapse = !this.isCollapse
		}
	}
}
</script>

<style lang="less" scoped>
.layouts {
	height: 100%;
	flex: 1;
	display: flex;
	flex-direction: column;
	.main {
		flex: 1;
		height: calc(100% - 55px);
		display: flex;
		background-color: #f3f6fd;
		.box {
			overflow: auto;
			display: flex;
			flex: 1;
			margin: 10px;
			border: 1px solid #e9edf5;
			border-radius: 4px;
			background-color: #fff;
		}
	}
}
</style>
