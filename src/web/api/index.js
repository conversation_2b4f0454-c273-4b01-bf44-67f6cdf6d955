/**
@Author: lin<PERSON><PERSON><PERSON>
* @description: web配置页面API
* @Date: 2024-05-28 17:06:18
* @Last Modified by: lin<PERSON><PERSON><PERSON>
* @Last Modified time: 2024-05-28 17:06:18
*/
import webRequest from '@/web/libraries/webRequest'
const uac = '/pureWeb'
export const uploadUrl = `${uac}/upload`

/**
 * 获取配置信息
 * @returns Promise
 */
export function getConfigInfo() {
	return webRequest({
		url: `${uac}/configList`,
		method: 'get'
	})
}

/**
 * 保存配置信息
 * @returns Promise
 */
export function setConfigInfo(data) {
	return webRequest({
		url: `${uac}/setLocalConfig`,
		method: 'post',
		data
	})
}

/**
 * 获取终端配置信息
 * @returns Promise
 */
export function getTerminalConfigInfo() {
	return webRequest({
		url: `${uac}/terminalConfig`,
		method: 'get'
	})
}

/**
 * 保存终端配置信息
 * @returns Promise
 */
export function setTerminalConfigInfo(data) {
	return webRequest({
		url: `${uac}/setTerminalConfig`,
		method: 'post',
		data
	})
}

/**
 * 重启终端
 * @returns Promise
 */
export function reloadApp() {
	return webRequest({
		url: `${uac}/reloadApp`,
		method: 'get'
	})
}
/**
 * 重启设备
 * @returns Promise
 */
export function reboot() {
	return webRequest({
		url: `${uac}/reboot`,
		method: 'get'
	})
}
/**
 * 下载日志
 * @returns Promise
 */
export function getLogs() {
	return webRequest({
		url: `${uac}/getLogs`,
		method: 'get',
		responseType: 'blob'
	})
}

/**
 * 获取终端和app 部分属性信息
 * @param {String} props
 * @returns Promise
 */
export function getProperty(props = '') {
	return webRequest({
		url: `${uac}/getProperty?searchProps=${props}`,
		method: 'get'
	})
}

/**
 * 下发升级
 * @returns Promise
 */
export function downUpgrade() {
	return webRequest({
		url: `${uac}/downUpgrade`,
		method: 'post'
	})
}

/**
 * 上传文件接口
 * @param {object} data 文件流格式
 */
export function fileUploader(data, headers) {
	return webRequest({
		url: uploadUrl,
		method: 'post',
		headers: { 'Content-Type': 'multipart/form-data', ...headers },
		data
	})
}

/**
 * 升级
 * @returns Promise
 */
export function upgrade(data) {
	return webRequest({
		url: `${uac}/upgrade`,
		method: 'post',
		data
	})
}
