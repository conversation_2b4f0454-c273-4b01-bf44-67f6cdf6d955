<template>
	<div class="cabinet-main" :style="{ 'justify-content': cabinetArr.length > 1 ? 'space-between' : 'space-around' }">
		<div v-for="item in cabinetArr" :key="item.sideCode" class="cabinet">
			<div class="title">{{ item.isMain ? '主柜' : item.name }}</div>
			<div class="cabinet-body">
				<div v-if="item.isMain" class="flex-door">
					<Row style="width: 50%">
						<Col v-if="ele.column == 1" span="24" v-for="(ele, index) in item.gridJsonList" :key="index" :class="[ele.gridSize, 'gridName isMain', ele.active ? 'active' : '']">{{ ele.gridName }}</Col>
					</Row>
					<Row style="width: 50%">
						<Col v-if="ele.column == 2 && index < 11" span="24" v-for="(ele, index) in item.gridJsonList" :key="index + 'A'" :class="[ele.gridSize, 'gridName', 'nodor', ele.active ? 'active' : '']">{{ ele.gridName }}</Col>
						<Col span="24" :class="['czt', 'gridName']">
							<p class="czjm">操作界面</p>
						</Col>
						<Col v-if="ele.column == 2 && index >10" span="24" v-for="(ele, index) in item.gridJsonList" :key="index + 'B'" :class="[ele.gridSize, 'gridName', ele.active ? 'active' : '']">{{ ele.gridName }}</Col>
					</Row>
				</div>
				<Row v-else>
					<Col span="12" v-for="(ele, index) in item.gridJsonList" :key="index + 'c'" :class="[ele.gridSize, 'gridName', ele.active ? 'active' : '']">{{ ele.gridName }}</Col>
				</Row>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	name: 'CabinetMain',
	props: {
		currentCabinetNum: {
			require: true,
			type: String,
			default: '' //A02,B03
		},
		openedCabinetNum: {
			require: true,
			type: Array
		},
		cabinetDetail: {
			require: true,
			type: Array
		}
	},
	data() {
		return {
			cabinetArr: [],
			titleSpan: 0
		}
	},
	watch: {
		cabinetDetail: {
			handler(newVal) {
				this.init()
			}
		},

		currentCabinetNum: {
			handler(n, o) {
				this.cabinetArr.forEach((item) => {
					if (item.gridJsonList && item.gridJsonList.length > 0) {
						item.gridJsonList.forEach((data) => {
							this.$set(data, 'active', false)
						})
					}
				})
				console.log(n, 'currentCabinetNum')
				if (n) {
                    this.init()
				}
			},
			deep: true,
			immediate: true
		}
	},
	created() {
		this.init()
	},
	methods: {
		init() {
			this.cabinetArr = []
			const params = {
				ip: this.serverConfig.iotAddress || this.serverConfig.ip
			}
			this.$Get(this.AmtCom.getCabinetInfo, params).then((res) => {
				if (res.success) {
					this.cabinetArr = res.data.cabinetList
					let arr = this.currentCabinetNum.split(',')
					console.log(arr, this.cabinetArr, 'arr---currentCabinetNum')
						arr.forEach((ele) => {
							if (this.cabinetArr && this.cabinetArr.length > 0) {
								this.cabinetArr.forEach((item) => {
									if (item.gridJsonList && item.gridJsonList.length > 0) {
										item.gridJsonList.forEach((data) => {
											if (ele == data.gridName) {
												this.$set(data, 'active', true)
											}
										})
									}
								})
								console.log(this.cabinetArr, ' this.cabinetArr')
							}
						})
				}
			})
			// this.cabinetDetail.map((item) => {
			//     const obj = {
			//         sideCode: item.sideCode,
			//         positionArr: []
			//     }
			//     obj.positionArr = this.dealCabinetDetail(item)
			//     this.cabinetArr.push(obj)
			//     console.log(this.cabinetArr)
			// })
		},
		dealCabinetDetail(data) {
			const colData = []
			data.details?.map((item) => {
				this.openedCabinetNum.indexOf(item.num) > -1 ? (item.open = true) : (item.open = false)
				if (colData.indexOf(item.colPos) == -1) colData.push(item.colPos)
			})
			colData.sort((a, b) => a - b)
			const posData = []

			colData.map((col) => {
				const cabinetData = {
					col
				}
				cabinetData.row = data.details.filter((item) => item.colPos == col)
				const totalMerges = cabinetData.row.reduce((newVal, item) => {
					return Number(newVal) + Number(item.merge)
				}, 0)
				console.log('totalMerges', totalMerges)
				cabinetData.row.forEach((item) => {
					item.flexBasis = item.merge / totalMerges
				})
				posData.push(cabinetData)
			})
			this.titleSpan = colData.length
			return posData
		}
	}
}
</script>

<style lang="less" scoped>
// @assets: '@/assets/images';
.active {
	background: #e5ac00 !important;
}
.gridName {
	height: 42px;
	background: #5c8ae5;
	text-align: center;
	font-family: ArialMT, ArialMT;
	font-weight: normal;
	font-size: 26px;
	color: #fff;
	border-bottom: 2px solid #fff;
	display: flex;
	align-items: center;
	justify-content: center;
}
.czjm {
	padding: 75px 20px;
	background: #5caee5;
}
.M {
	height: 104px;
}
.L {
	height: 130px;
}
.gridName:nth-child(2n + 1) {
	border-right: 2px solid #fff;
}
.nodor {
	border-right: 2px solid transparent !important;
}
.isMain {
	border-right: 2px solid #fff !important;
}
.cabinet-main {
	width: 840px;
	height: 712px;
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin: 40px auto;
	.cabinet {
		width: calc(50% - 16px);
		.title {
			width: 100%;
			height: 60px;
			background: #4d5f80;
			margin-bottom: 4px;
			color: #fff;
			font-size: 28px;
			line-height: 60px;
			text-align: center;
		}
		.cabinet-body {
			width: 100%;
			height: 712px;
			// display: flex;
			justify-content: space-between;
			margin: 0 auto;
			.col {
				height: 100%;
				flex: 1;
				margin-right: 4px;
				display: flex;
				justify-content: space-between;
				flex-direction: column;
				&:last-child {
					margin-right: 0;
				}
				.row {
					flex: 1;
					background-color: #5c8ae6;
					// margin-bottom: 4px;
					border-bottom: 4px solid #fff;
					div {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 100%;
						height: 100%;
						font-size: 32px;
						color: #fff;
						&.active {
							background-color: #e6ac00;
							animation: borderColor 1s infinite alternate;
							line-height: 1;
							box-sizing: border-box;
						}
						// &.activeAnimation {
						//   animation: borderColor 1s infinite  alternate;
						// }
					}
					&.screen {
						display: flex;
						justify-content: center;
						align-items: flex-start;
						background-color: #99d5ff;
						div {
							display: block;
							width: 100%;
							height: 50%;
							// background: url('@/assets/images/zg_icon.png') no-repeat;
							background-size: 100% 100%;
							background-position: 0 0;
							border-bottom: 2px solid #77b8e6;
						}
					}
					&:last-of-type.screen {
						border: none;
					}
					// &.active{
					//   background-color: #E6AC00;
					//   animation: borderColor 1s infinite  alternate;
					// }
				}
			}
		}
	}
}

@keyframes borderColor {
	from {
		border: 4px solid #e6ac00;
	}

	to {
		border: 4px solid #cc8e0f;
	}
}
.flex-door {
	display: flex;
}
.czt {
	height: 295px;
	border-right: 1px solid transparent !important;
	background: #99d4ff !important;
}
</style>
