<template>
	<div id="head" class="flex flSp">
		<div class="title1 w70">
			<!-- <i class="logo" :class="{ hj: serverConfig.isHaiJing }" @dblclick="openDialog('config')"></i> -->
			<i class="location"></i><span>{{ title1 }}</span>
		</div>
		<div class="flex  w30">
			<div class="time-line">
				<p class="curTime">{{ curTimeData }}</p>
				<p class="curDate">{{ curDate }}</p>
			</div>
			<i class="setting" v-if="this.$route.path =='/home'" @click="drupSetting"></i>
		</div>
		<!-- <div class="flex title2">{{ title2 }}</div> -->
		<!-- <div class="versionClickView" @dblclick="openDialog('version')"></div> -->
	</div>
</template>

<script>
import Config from '@/components/setConfig'
import Version from '@/components/version'
import store from '@/stroe'

export default {
	name: 'Head',
	// inject: ['updateAppInfo'],
	data() {
		return {
			title1: '北京市第三看守所',  //XX市XXX分局XXX派出所
			title2: '智能物管柜管理系统',
			dialogObj: null,
			serverConfig: {
				policeName: '北京市第三看守所',
			},
			curTime: new Date(),
			curDate: new Date().getFullYear() + '-' + Number(new Date().getMonth() + 1) + '-' + new Date().getDate(),
			curWeek: '',
			curTimeData: new Date().toLocaleTimeString(),
			timer: null,	

		}
	},
	mounted() {
		// this.getWeek()
		this.timer = setInterval(() => {
			this.curTimeData = new Date().toLocaleTimeString();
		}, 1000); // 每秒更新一次时间
		this.serverConfig = store.state.config.terminalConfig
		console.log(store.state.config.terminalConfig,'store.state.config.terminalConfig')
		if (this.serverConfig) {
			const { policeName, appName,name } = this.serverConfig
			console.log(this.serverConfig,'this.serverConfig')
			this.title1 = name
			this.title2 = appName
		}
	},
	beforeDestroy() {
		clearInterval(this.timer); // 组件销毁前清除定时器
	},
	methods: {
		drupSetting() {
			this.$router.push({
				path:'systemConfiguration',// 'systemConfiguration',//'systemConfiguration',
				query: {
					fromPath: '/home',
					toPath: '/systemConfiguration'
				}
			})
		},
		closeDialog() {
			this.dialogObj && this.dialogObj.close()
			this.dialogObj = null
		},
		openDialog(type) {
			this.closeDialog()
			const componentMap = {
				config: {
					title: '系统配置',
					component: Config,
					componentParams: { isWeb: false }
				},
				version: {
					title: '版本信息',
					component: Version,
					componentParams: {}
				}
			}
			const currentComponent = componentMap[type]
			this.dialogObj = this.$GxxDialog({
				title: currentComponent.title,
				showHeader: true,
				showHeaderClose: false,
				component: currentComponent.component,
				componentParams: currentComponent.componentParams
			})
			this.dialogObj.on.then((res) => {
				this.dialogObj = null
				if (type == 'config' && res.type == 'confirm') {
					// this.updateAppInfo(res.data)
				}
			})
		}
	},
	beforeDestroy() {
		this.closeDialog()
	}
}
</script>

<style lang="less" scoped>
@assets: '../assets/';

#head {
	width: 100%;
	// height: 360px;
	// background: #4f5eff;
	// background: url('@{assets}/images/head_text_icon.png') no-repeat;
	height: 80px;
	background: linear-gradient(180deg, #337FFF 0%, rgba(153, 199, 252, 0.37) 100%);
	background-size: 100% 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	padding: 20px 20px;

	.flex {

		display: flex;
		align-items: center;
		font-weight: bold;
		color: #ebf5ff;
		letter-spacing: 3px;
	}

	.flSp {
		justify-content: space-between;
	}

	.title1 {
		font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
		font-weight: bold;
		font-size:30px;
		color: #465C88;
		height: 40px;
		align-items: center;
		display: flex;

		.logo {
			display: inline-block;
			width: 95px;
			height: 96px;
			background-image: url('@{assets}/images/logo.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			margin-right: 32px;
			margin-top: 15px;

			&.hj {
				background-image: url('@{assets}/images/logo_haijing.png');
			}
		}
	}

	.title2 {
		font-size: 75px;
		margin-top: 25px;
		line-height: 60px;
		-webkit-box-reflect: below 10px -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(255, 255, 255, 0.31)));
	}
}

.location {
	width: 43px;
	height: 43px;
	display: inline-block;
	background-image: url('@{assets}/images/location.png');
	background-size: 100% 100%;
}

.setting {
	width:  43px;
	height: 43px;
	display: inline-block;
	background-image: url('@{assets}/images/setting.png');
	background-size: 100% 100%;
	margin-left: 5px;
	cursor: pointer;
	border-left: 1px solid #77ADFD;
}

.time-line {

	padding-right: 6px;
	text-align: right;
}

.curTime {
	font-family: SourceHanSansCN-Normal, SourceHanSansCN-Normal;
	font-weight: normal;
	font-size: 26px;
	color: #5779B3;
	line-height: 18px;
}

.curDate {
	font-family: SourceHanSansCN-Normal, SourceHanSansCN-Normal;
	font-weight: normal;
	font-size: 20px;
	color: #5779B3;
	// line-height: 18px;
}

.w30 {
	width: 40%;
	justify-content: flex-end;
}

.w70 {
	width: 60%;
}
</style>
