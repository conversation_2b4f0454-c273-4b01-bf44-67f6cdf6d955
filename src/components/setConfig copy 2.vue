<template>
		<div >
			<headNav :show-box-index="[true, true, true]"></headNav>
			<!-- <app-config ref="appConfigRef" :params="params" @changeUpgradType="changeUpgradType"></app-config> -->
			<systemConfiguration ref="appConfigRef" :params="params" @changeUpgradType="changeUpgradType" />
			<!-- <iot-config ref="iotConfigRef" :params="params" :isRequired="upgradType == 'iot'"></iot-config> -->
			<div class="x-dialog-footer" v-if="!params.isWeb">
				<gxx-button type="info" @click="cancel">取消</gxx-button>
				<gxx-button @click="confirm">确定</gxx-button>
			</div>
		</div>
</template>

<script>
import AppConfig from './config/app'
import IotConfig from './config/iot'
import systemConfiguration from './config/systemConfiguration.vue'
import headNav from '_c/headNav'

export default {
	name: 'Config',
	props: ['params'],
	components: {
		AppConfig,
		IotConfig,
		systemConfiguration, headNav
	},
	data() {
		return {
			upgradType: '',
			configParams: {},
			config: this.$store.getters['getTerminalConfig']
		}
	},
	created() {
		this.upgradType = this.params.isWeb ? this.params.configParams?.upgradType : this.config.upgradType
	},
	methods: {
		changeUpgradType(obj) {
			this.upgradType = obj.value
		},
		cancel() {
			if (this.validate('cancel')) {
				this.$parent.close('cancel')
			}
		},
		confirm() {
			if (this.validate('confirm')) {
				this.$emit('getReturn', this.configParams)
				this.$parent.close('confirm')
				this.$GxxMessage({ message: '配置成功！', type: 'success' })
			}
		},
		validate(type) {
			const appConfigRef = this.$refs.appConfigRef
			// const appValid = appConfigRef.validate(type)
			// if (!appValid) {
			// 	return
			// }
			Object.assign(this.configParams, appConfigRef.configParams)
			const iotConfigRef = this.$refs.iotConfigRef
			const iotValid = iotConfigRef.validate(type)
			if (!iotValid) {
				return false
			}
			Object.assign(this.configParams, iotConfigRef.configParams)
			return true
		}
	}
}
</script>

<style lang="less" scoped>
.dialog {
	width: 900px;
}

.x-dialog-footer {
	margin: 20px 0;
	padding: 0;

	button {
		width: auto;
	}
}
</style>
