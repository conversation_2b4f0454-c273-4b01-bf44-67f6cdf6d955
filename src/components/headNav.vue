<template>
	<div class="headTop">
		<div class="item">
			<div v-if="showBoxIndex[0]" class="backHome" @click="goBackAction">
				<i class="goBackBtn"></i>
				返回
			</div>
		</div>
		<div class="item center">
			<div v-if="showBoxIndex[1]" class="TopMenuCancelBtn">
				<span class="outTime">{{ countdown }}</span>
				秒后自动退出
			</div>
			<div v-if="sysTitle" class="sysTitle">{{ sysTitle }}</div>
		</div>
		<div class="item end">
			<div v-if="showBoxIndex[2]" class="backHome" @click="backHomeAction">
				首页
				<i class="backHomeBtn"></i>
			</div>
		</div>
	</div>
</template>

<script>
import { removeNewToken } from '@/libs/utils.js'
export default {
	props: {
		showBoxIndex: {
			type: Array,
			default() {
				return [true, true, true]
			}
		},
		sysTitle: String
	},
	data() {
		return {
			countdown: this.serverConfig.loginOutTime || 120,
			timer: null
		}
	},
	created() {
		this.countdownAction()
	},
	methods: {
		countdownAction() {
			if (!this.showBoxIndex[1]) return false
			this.timer = setInterval(() => {
				this.countdown--
				this.$emit('countDown', this.countdown)
				if (this.countdown == 0) {
					this.countdown = 0
					this.backHomeAction()
				}
			}, 1000)
		},
		goBackAction() {
			if (this.$route.path == '/scanCode' || this.$route.path == '/selfbatchTakeOut') {
				this.$router.push({ path: '/faceLogin', query: { toPath: this.$route.path } })
			} else {
				this.$router.go('-1')
			}
		},
		clearTimer() {
			this.timer && clearInterval(this.timer)
			this.timer = null
		},
		backHomeAction() {
			this.clearTimer()
			localStorage.removeItem('userInfo') // 清除登录之后的用户信息
			removeNewToken() // 清除登录之后的新token
			this.$Modal && this.$Modal.remove()
			this.$router.push('/home')
		}
	},
	beforeDestroy() {
		this.clearTimer()
	}
}
</script>

<style scoped lang="css">
.TopMenuCancelBtn {
	color: #5779b2;
	font-size: 32px;
	line-height: 80px;
}

.outTime {
	color: #e60012;
	margin-right: 10px;
	font-size: 32px;
}
</style>
<style lang="less" scoped>
@assets: '../assets/images';

.headTop {
	width: 100%;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24px 48px 0;
	box-sizing: border-box;

	.item {
		width: 33%;
		display: flex;

		&.center {
			justify-content: center;
		}

		&.end {
			justify-content: flex-end;
		}
	}
}

.countdown {
	margin-right: 20px;
	font-size: 32px;
	text-align: right;
	color: #eb5e75;
	line-height: 80px;
}

.sysTitle {
	font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
	font-weight: bold;
	font-size: 38px;
	color: #2B3346;
	line-height: 36px;
}
</style>
