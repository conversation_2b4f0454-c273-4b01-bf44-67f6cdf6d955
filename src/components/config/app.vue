<template>
	<div class="config-module">
		<div class="gxx-subtitle">基础配置</div>
		<div class="module-content">
			<div class="content-row">
				<div class="row-label">物品柜编号</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.cabinetCode" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">服务地址</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.server" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">服务版本</div>
				<span class="row-colon">:</span>
				<gxx-radio value="3" v-model="configParams.serverVersion">v3</gxx-radio>
				<gxx-radio value="5" v-model="configParams.serverVersion">v5</gxx-radio>
			</div>
			<div class="content-row" v-if="configParams.serverVersion == 3">
				<div class="row-label">物管中心id</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.centerId" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">终端应用名称</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.appName" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">派出所名称</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.policeName" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">登录退出时长</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.loginOutTime" v-xKeyboard></gxx-input>
			</div>
		</div>
		<div class="gxx-subtitle">硬件配置</div>
		<div class="module-content">
			<div class="content-row">
				<div class="row-label">补光灯类型</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="lightList" v-model="configParams.lightType"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">补光灯协议</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="lockControlProtocolList" :disabled="isDisabledLightControlProtocol" v-model="configParams.lightControlProtocol"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">补光灯{{ configParams.lightControlProtocol == 1 ? '地址' : '串口' }}</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.lightCom" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">自动开启补光灯</div>
				<span class="row-colon">:</span>
				<gxx-radio value="1" v-model="configParams.openFillLight">是</gxx-radio>
				<gxx-radio value="0" v-model="configParams.openFillLight">否</gxx-radio>
			</div>
			<div class="content-row">
				<div class="row-label">柜锁类型</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="lockTypeList" v-model="configParams.lockType"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">智能柜协议</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="lockControlProtocolList" :disabled="isDisabledLockControlProtocol" v-model="configParams.lockControlProtocol"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">智能柜{{ configParams.lockControlProtocol == 1 ? '地址' : '串口' }}</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.lockCom" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">智能柜锁控板起始号</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.lockPlateStartIndex" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">智能柜锁控板规格</div>
				<span class="row-colon">:</span>
				<gxx-popover placement="top" trigger="click" width="700" content="按单个锁控板连接的最大柜格数配置，同一副柜按英文逗号分隔,不同副柜按英文分号分隔。例如：柜子A有一个锁控板，连接10个柜格;柜子B有两个锁控板，分别连接10个、10个柜格;则此处配置为10;10,10">
					<span slot="reference" class="doubt">?</span>
				</gxx-popover>
				<gxx-input type="text" v-model="configParams.cabinetConfig" v-xKeyboard></gxx-input>
			</div>
		</div>
		<div class="gxx-subtitle">人脸配置</div>
		<div class="module-content">
			<div class="content-row">
				<div class="row-label">人脸库名称</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.bspFaceLoginCode" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">人像特征算法</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="photoshopModeList" v-model="configParams.photoshopMode"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">人脸摄像头</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="devicesList" labelField="label" valueField="deviceId" v-model="configParams.faceCamera"></gxx-select>
			</div>
			<div class="content-row">
				<div class="row-label">摄像旋转角度</div>
				<span class="row-colon">:</span>
				<gxx-input type="text" v-model="configParams.rotate" v-xKeyboard></gxx-input>
			</div>
			<div class="content-row">
				<div class="row-label">摄像水平翻转</div>
				<span class="row-colon">:</span>
				<gxx-radio value="Y" v-model="configParams.horizontal">是</gxx-radio>
				<gxx-radio value="N" v-model="configParams.horizontal">否</gxx-radio>
			</div>
		</div>
		<div class="gxx-subtitle">升级配置</div>
		<div class="module-content">
			<div class="content-row">
				<div class="row-label">升级类型</div>
				<span class="row-colon">:</span>
				<gxx-select :enableFilter="false" :enableEmpty="false" :option="upgradTypeOption" v-model="configParams.upgradType" @change="changeUpgradType"></gxx-select>
			</div>
		</div>
	</div>
</template>

<script>
import { lockTypeList } from '@/utils/common_dict'
import { deepClone, getMediaDevices } from '@/utils/normal_util'
export default {
	name: 'AppConfig',
	props: ['params'],
	computed: {
		isDisabledLightControlProtocol() {
			return this.configParams.lightType == 3
		},
		isDisabledLockControlProtocol() {
			return this.configParams.lockType == 2 || this.configParams.lockType == 3
		}
	},
	data() {
		return {
			config: this.$store.getters['getTerminalConfig'],
			// 补光灯类型
			lightList: [
				{
					value: 1,
					label: 'GXX-K32-1'
				},
				{
					value: 3,
					label: 'GXX-D99-1'
				}
			],
			// 智能柜类型
			lockTypeList,
			// 智能柜协议
			lockControlProtocolList: [
				{
					value: 1,
					label: 'webSocket'
				},
				{
					value: 2,
					label: '串口'
				}
			],
			// 人脸算法
			photoshopModeList: [
				{
					value: -1,
					label: '无'
				},
				{
					value: 1,
					label: 'GXX-FACE-1'
				}
			],
			// 升级类型
			upgradTypeOption: [
				{
					label: '默认',
					value: 'default'
				},
				{
					label: '智能硬件云平台',
					value: 'iot'
				}
			],
			devicesList: [],
			configParams: {
				appName: '',
				policeName: '',
				server: '',
				cabinetCode: '',
				patternType: '0',
				serverVersion: '5',
				centerId: '',
				loginOutTime: 120,
				// 补光灯类型
				lightType: 3,
				lightControlProtocol: 2,
				lightCom: '',
				openFillLight: '0',
				// 智能类型
				lockType: 3,
				lockControlProtocol: 2,
				lockPlateStartIndex: 1,
				cabinetConfig: '',
				lockCom: '',
				// 摄像
				bspFaceLoginCode: '',
				photoshopMode: 1,
				faceCamera: '',
				horizontal: 'N',
				rotate: 0,
				upgradType: 'default'
			}
		}
	},
	async created() {
		if (this.params.isWeb) {
			this.configParams = (await deepClone(this.params.configParams)) || {}
			this.devicesList = (await deepClone(this.params.faceDeviceList)) || []
		} else {
			this.configParams = (await deepClone(this.config)) || {}
			this.getDevices()
		}
	},
	methods: {
		changeUpgradType(item) {
			this.$emit('changeUpgradType', item)
		},
		async getDevices() {
			this.devicesList = []
			const devices = await getMediaDevices()
			this.devicesList = devices || []
		}
	},
	watch: {
		'configParams.serverVersion'(val) {
			if (val == 3) {
				this.configParams.patternType = '0'
			}
		},
		'configParams.lightType'() {
			if (this.isDisabledLightControlProtocol && this.configParams.lightControlProtocol != 2) {
				this.configParams.lightControlProtocol = 2
			}
		},
		'configParams.lockType'() {
			if (this.isDisabledLockControlProtocol && this.configParams.lockControlProtocol != 2) {
				this.configParams.lockControlProtocol = 2
			}
		},
		'configParams.lightControlProtocol'(val) {
			/**
			 * ia32_1_1：windows GXX-K32-1 webSocket
			 * ia32_1_2：windows GXX-K32-1 串口
			 * x64_1_1：x64 GXX-K32-1 webSocket
			 * x64_1_2：x64 GXX-K32-1 串口
			 * x64_3_2：x64 GXX-D99-1 串口
			 * arm64_3_2：arm64 GXX-D99-1 串口
			 */
			const comMap = {
				ia32_1_1: 'ws://12*******:24246/light',
				ia32_1_2: 'COM3',
				ia32_3_2: 'COM3',
				x64_1_1: 'ws://12*******:8540/light',
				x64_1_2: '/dev/ttyS0',
				x64_3_2: '/dev/ttyS2',
				arm64_3_2: '/dev/ttyS3'
			}
			this.configParams.lightCom = comMap[`${SYSTEM_ARCH}_${this.configParams.lightType}_${val}`] || this.configParams.lightCom
		},
		'configParams.lockControlProtocol'(val) {
			/**
			 * ia32_1_1：windows GXX-K32-1 webSocket
			 * ia32_1_2：windows GXX-K32-1 串口
			 * x64_1_1：x64 GXX-K32-1 webSocket
			 * x64_1_2：x64 GXX-K32-1 串口
			 * x64_2_2：x64 GXX-J30-1 串口
			 * x64_3_2：x64 GXX-D99-1 串口
			 * arm64_3_2：arm64 GXX-D99-1 串口
			 */
			const comMap = {
				ia32_1_1: 'ws://12*******:24245/locker',
				ia32_1_2: 'COM3',
				ia32_2_2: 'COM3',
				ia32_3_2: 'COM3',
				x64_1_1: 'ws://12*******:8540/locker',
				x64_1_2: '/dev/ttyS0',
				x64_2_2: 'COM3',
				x64_3_2: '/dev/ttyS2',
				arm64_3_2: '/dev/ttyS3'
			}
			this.configParams.lockCom = comMap[`${SYSTEM_ARCH}_${this.configParams.lockType}_${val}`] || this.configParams.lockCom
		}
	}
}
</script>

<style lang="less" scoped>
@import url('./common.less');
</style>
