<template>
	<div id="FileInventory">
		<headNav :show-box-index="[true, false, true]" :sysTitle="sysTitle"></headNav>
		<div class="setting-wrap">
			<p class="settingTitle">基本信息配置</p>
			<div class="module-content">
				<div class="content-row">
					<div class="row-label">监所名称：</div>
					<!-- <span class="row-colon"></span> -->
					<!-- <gxx-input class="gxx-input" type="text" v-model="configParams.policeName" v-xKeyboard></gxx-input> -->
					<gxx-select :enableFilter="false" :enableEmpty="false" :option="lockControlProtocolList" v-model="configParams.orgName"></gxx-select>
				</div>
				<div class="content-row">
					<div class="row-label">设备名称：</div>
					<!-- <span class="row-colon">:</span> -->
					<gxx-input class="gxx-input" type="text" v-model="configParams.name" v-xKeyboard></gxx-input>
				</div>
				<div class="content-row">
					<div class="row-label">版 &nbsp; &nbsp; &nbsp; 权：</div>
					<span class="row-colon text-Setting" style="line-height: 60px">{{ configParams.copyright }}</span>
				</div>
				<div class="content-row">
					<div class="row-label">开柜方式：</div>
					<gxx-select :enableFilter="false" :enableEmpty="false" :option="cabinetOpenMethodList" v-model="configParams.cabinetOpenMethod"></gxx-select>

					<!-- <gxx-input class="gxx-input" type="text" v-model="configParams.cabinetOpenMethodName" v-xKeyboard></gxx-input> -->
				</div>
				<div class="content-row">
					<div class="row-label">操作时间：</div>
					<gxx-input class="gxx-input"  type="text" v-model="configParams.operationTime" v-xKeyboard><span class="min-time">&nbsp;&nbsp;秒</span></gxx-input>
					
				</div>
				<div class="content-row">
					<div class="row-label">相机设置：</div>
					<!-- <span class="row-colon">:</span> -->
					<Button class="gxx-input" type="primary" style="width: 200px">相机设置</Button>
				</div>
			</div>
			<p class="settingTitle">设备信息配置</p>

			<div class="module-content">
				<div class="content-row">
					<div class="row-label">mac&nbsp;地&nbsp;址：</div>
					<!-- <span class="row-colon"></span> -->
					<gxx-input class="gxx-input" type="text" v-model="configParams.mac" disabled v-xKeyboard></gxx-input>
				</div>
				<div class="content-row">
					<div class="row-label">本 &nbsp; 机&nbsp; IP：</div>
					<!-- <span class="row-colon">:</span> -->
					<gxx-input class="gxx-input" type="text" v-model="configParams.ip" disabled v-xKeyboard></gxx-input>
				</div>
				<div class="content-row">
					<div class="row-label">版&nbsp; 本&nbsp;&nbsp; 号：</div>
					<!-- <span class="row-colon">:</span> -->
					<gxx-input class="gxx-input" type="text" v-model="configParams.versionNumber" disabled v-xKeyboard></gxx-input>
				</div>
			</div>
		</div>
		<div class="x-dialog-footer" style="width: 100%">
			<!-- <gxx-button type="info" @click="cancel">取消</gxx-button> -->
			<gxx-button @click="updateConfig" class="saveSetting">保 存</gxx-button>
		</div>
	</div>
</template>

<script>
import headNav from '_c/headNav'

export default {
	components: {
		headNav
	},
	data() {
		return {
			sysTitle: '系统设置',
			configParams: {},
			lockControlProtocolList: [],
			upgradType: '',
			config: this.$store.getters['getTerminalConfig'],
			cabinetOpenMethodList:[
				{label:'刷脸',value:1},
				{label:'账号',value:2},
				{label:'手势',value:3},
			]
		}
	},
	created() {
		this.upgradType = this.params && this.params.isWeb ? this.params.configParams?.upgradType : this.config.upgradType
		this.getConfig()
	},
	methods: {
		changeUpgradType(obj) {
			this.upgradType = obj.value
		},
		cancel() {
			if (this.validate('cancel')) {
				this.$parent.close('cancel')
			}
		},
		confirm() {
			if (this.validate('confirm')) {
				this.$emit('getReturn', this.configParams)
				this.$parent.close('confirm')
				this.$GxxMessage({ message: '配置成功！', type: 'success' })
			}
		},
		getConfig() {
			const params = {
				ip: this.serverConfig.iotAddress  || this.serverConfig.ip
			}
			this.$Get(this.AmtCom.getCabinetISetting, params).then((res) => {
				if (res.success) {
					this.configParams = res.data
				}
			})
		},
		updateConfig(){
			let params=this.configParams
			this.$Post(this.AmtCom.updateCabinetSetting, params).then((res) => {
				if (res.success) {
					this.$GxxMessage({ message: '保存成功！', type: 'success' })
					this.updateAppInfo(this.configParams)
					this.$router.push('/home')
				}else{

				}
			})
		},
		updateAppInfo(config, isReload = true) {
            this.writeConfigFile(config)
            this.setStoreConfig(config)
            if (isReload) {
                sessionStorage.clear()
                window.location.reload()
            }
        },
        setStoreConfig(config) {
            this.logger('网页设置的配置', config)
            this.$store.commit('setTerminalConfig', config)
        },
        writeConfigFile(config) {
            ipcRenderer && ipcRenderer.send('setTermConfig', config)
        },
		validate(type) {
			const appConfigRef = this.$refs.appConfigRef
			// const appValid = appConfigRef.validate(type)
			// if (!appValid) {
			// 	return
			// }
			Object.assign(this.configParams, appConfigRef.configParams)
			const iotConfigRef = this.$refs.iotConfigRef
			const iotValid = iotConfigRef.validate(type)
			if (!iotValid) {
				return false
			}
			Object.assign(this.configParams, iotConfigRef.configParams)
			return true
		}
	}
}
</script>


<style lang="less" scoped>
.saveSetting {
	width: 90% !important;
	height: 96px;
	background: #2b5fda;
	font-family: MicrosoftYaHei, MicrosoftYaHei;
}

/deep/ .gxx-button-content {
	font-weight: normal;
	font-size: 36px !important;
	text-align: center;
	margin: auto;
}

.settingTitle {
	font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
	font-weight: normal;
	font-size: 40px;
	color: #2b3346;
	line-height: 36px;
	text-align: left;
	margin: 48px 0;
}

.setting-wrap {
	margin: 0 40px;
}

.content-row {
	display: flex;
	margin: 30px 0;

	.row-label {
		font-family: MicrosoftYaHei, MicrosoftYaHei;
		font-weight: normal;
		font-size: 40px;
		color: #5f709a;
		line-height: 60px;
		width: 250px;
		display: flex;
		justify-content: space-between;
	}

	.gxx-input {
	}
}
.gxx-input-container /deep/ input {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 26px;
	color: #3663b3;
}

.min-time {
	line-height: 60px;
	font-size: 26px;
	color: #2b5fda;
}

.gxx-input-container {
	width: calc(~'100% - 260px');
	height: 60px;
}

.gxx-select-wrap {
	height: 60px !important;
	line-height: 60px !important;
	width: calc(~'100% - 260px') !important;
}
/deep/ .select-input {
	height: 60px !important;
	line-height: 60px !important;
	// width: calc(~"100% - 260px") !important;
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 26px;
	color: #3663b3;
}
.text-Setting {
	font-family: MicrosoftYaHei, MicrosoftYaHei;
	font-weight: normal;
	font-size: 26px;
	color: #3663b3;
}
</style>
