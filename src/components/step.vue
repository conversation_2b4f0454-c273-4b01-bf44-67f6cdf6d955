<template>
    <div v-longpress="handleReflash" class="step-warp">
        <div class="common-tool">
            <div v-if="!isHome" ref="backBtn" class="back-home" @click="handleBack">首页</div>
            <div class="user-name">
                当前用户:
                <span>{{ user.name || '' }}</span>
            </div>
            <div v-if="!stopCountDown" class="logout-warp">
                <span class="count-down">{{ count }}</span>
                <div class="logout" @click="handleLogout">注销</div>
            </div>
        </div>
        <template v-if="!isHome">
            <div v-if="!isTakeOut" class="step-list">
                <i :class="{ done: currentStep > 1, active: currentStep == 1 }">{{ currentStep > 1 ? '' : 1 }}</i>
                <span :class="{ done: currentStep > 1 }"></span>
                <i :class="{ done: currentStep > 2, active: currentStep == 2 }">{{ currentStep > 2 ? '' : 2 }}</i>
                <span :class="{ done: currentStep > 2 }"></span>
                <i :class="{ done: currentStep > 3, active: currentStep == 3 }">{{ currentStep > 3 ? '' : 3 }}</i>
                <span :class="{ done: currentStep > 3 }"></span>
                <i :class="{ done: currentStep > 4, active: currentStep == 4 }">{{ currentStep > 4 ? '' : 4 }}</i>
                <span :class="{ done: currentStep > 4 }"></span>
                <i :class="{ done: currentStep > 5, active: currentStep == 5 }">{{ currentStep > 5 ? '' : 5 }}</i>
            </div>
            <div v-else class="step-list out">
                <i :class="{ out: true, done: currentStep > 1, active: currentStep == 1 }">{{ currentStep > 1 ? '' : 1 }}</i>
                <span :class="{ done: currentStep > 1 }"></span>
                <i :class="{ out: true, done: currentStep > 2, active: currentStep == 2 }">{{ currentStep > 2 ? '' : 2 }}</i>
                <span :class="{ done: currentStep > 2 }"></span>
                <i :class="{ out: true, done: currentStep > 3, active: currentStep == 3 }">{{ currentStep > 3 ? '' : 3 }}</i>
            </div>
        </template>
    </div>
</template>

<script>
import { removeNewToken } from '@/libs/utils'
export default {
    inject: ['CountDown'],
    props: {
        currentStep: {
            type: [Number, String],
            default: 1
        },
        // 是否首页
        isHome: {
            type: Boolean
        },
        // 是否存入/取出
        isTakeOut: {
            type: Boolean
        },
        // 禁止倒计时
        stopCountDown: {
            type: Boolean,
            default: false
        },
        save: {
            type: Function,
            default: null
        },
        network: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            count: ''
        }
    },
    computed: {
        user() {
            return this.$store.getters.getUser || {}
        }
    },
    mounted() {
        !this.stopCountDown && this.handleActionCountDown()
    },
	beforeDestroy() {
		!this.stopCountDown && this.handleClearTimer()
	},
    methods: {
        handleActionCountDown() {
            console.log('启动了', this.currentStep)
            this.CountDown.action(
                (count) => {
                    this.count = count + 'S'
                },
                () => {
                    !this.stopCountDown && this.handleLogout()
                }
            )
        },
        handleBack() {
            if (!this.isTakeOut && this.currentStep == 5) {
                this.$emit('backHome')
            } else if (this.isTakeOut && this.currentStep == 3) {
                this.$emit('backHome')
            } else {
                this.$emit('backHome')
                setTimeout(() => {
                    this.$router.push({ name: 'home' })
                })
            }
        },
        async handleLogout() {
            if (!this.network) return
            if (this.save) {
                this.save(() => {
                    this.callBackHome()
                })
            } else {
                this.callBackHome()
            }
        },
        callBackHome() {
            this.$Post(this.BspApi.logout)
            this.$store.commit('removeUser')
            removeNewToken()
            this.handleClearAll()
            const selector = document.querySelector('.x-dialog-wrap')
            selector && document.body.removeChild(selector)
            // this.$store.state.config.globalModalObject && this.$store.state.config.globalModalObject.hide()
            if (this.$route.name != 'home') {
                this.$router.push({ name: 'home' })
            }
        },
        handleReflash() {
            window.location.reload()
        },
        handleClearAll() {
            for (let i = 0; i < 999; i++) {
                clearInterval(i)
            }
        },
        handleClearTimer() {
            console.log('柜门页清除定时器')
            this.CountDown.clear()
        },
        // 断网状态下清除用户信息和定时器
        clearUserInfo() {
            removeNewToken()
            this.$store.commit('removeUser')
            this.handleClearAll()
        }
    }
}
</script>

<style lang="less" scoped>
@assets: '~@/assets/images/temp';
.step-warp {
	width: 100%;
	margin: 0 auto;
	padding: 0 48px 40px;
	box-sizing: border-box;
	display: none;
	.common-tool {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 24px;
		.back-home {
			color: #1269ff;
			font-size: 26px;
			width: 200px;
			height: 80px;
			border-radius: 80px;
			background-color: #fff;
			display: flex;
			justify-content: center;
			align-items: center;
			cursor: pointer;
			&::before {
				content: '';
				display: inline-block;
				width: 48px;
				height: 48px;
				background-image: url('@{assets}/back-home-icon.png');
				background-size: 100% 100%;

				margin-right: 8px;
			}
		}
		.user-name {
			font-size: 32px;
			color: #64759a;
			span {
				font-size: 32px;
				color: #337eff;
			}
		}
		.logout-warp {
			display: flex;
			align-items: center;
			justify-content: center;
			.count-down {
				font-size: 32px;
				color: #fc3e51;
				margin-right: 20px;
			}
			.logout {
				width: 200px;
				height: 80px;
				font-size: 26px;
				color: #fc3e51;
				background-color: #fff;
				border-radius: 80px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				&::after {
					content: '';
					display: inline-block;
					width: 48px;
					height: 48px;
					background-image: url('@{assets}/logout-icon.png');
					background-size: 100% 100%;
					margin-left: 8px;
				}
			}
		}
	}
	.step-list {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 32px;
		margin-top: 32px;
		i {
			display: inline-flex;
			width: 48px;
			height: 48px;
			color: #c8d1e5;
			font-size: 32px;
			font-weight: bold;
			border: 1px solid #c8d1e5;
			border-radius: 48px;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
			position: relative;
			&.active {
				color: #fff;
				border-color: #39cc7e;
				background-color: #39cc7e;
				&::before {
					color: #191e3b;
					font-size: 36px;
					font-weight: bold;
				}
			}
			&.done {
				border: none;
				background-size: 100% 100%;
				background-image: url('@{assets}/step-success.png');
			}

			&::before {
				position: absolute;
				width: 180px;
				color: #b6bed0;
				font-weight: normal;
				font-size: 28px;
				left: 50%;
				transform: translateX(-50%);
				text-align: center;
				top: 60px;
				line-height: 1;
			}
			&.out {
				&:nth-child(1)::before {
					content: '输入取件码';
				}
				&:nth-child(3)::before {
					content: '确认信息';
				}
				&:nth-child(5)::before {
					content: '开柜取物';
				}
			}
			&:nth-child(1)::before {
				content: '选择物品';
			}
			&:nth-child(3)::before {
				content: '拍摄照片';
			}
			&:nth-child(5)::before {
				content: '选择取货人';
			}
			&:nth-child(7)::before {
				content: '选择库位';
			}
			&:nth-child(9)::before {
				content: '存入物品';
			}
		}
		span {
			width: 20%;
			height: 4px;
			background-color: #c8d1e5;
			border-radius: 4px;
			margin: 0 10px;
			&.done {
				background-color: #39cc7e;
			}
		}
	}
}
</style>
