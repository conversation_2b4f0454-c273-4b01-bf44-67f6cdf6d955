<template>
    <div id="viewBox">
        <Head></Head>
        <router-view v-if="isRouterAlive" :key="$route.path"></router-view>
        <!-- <mqtt ref="mqttRef"></mqtt> -->
    </div>
</template>

<script>
import Head from '_c/head'
import mqtt from '@/views/mqtt/index.vue'
import Lock from '@/libs/lock/index'
import { getGHEvent, cabinetToBoxAndDoor } from '@/libs/lock/util'
const ipcRenderer = window.electronAPI?.ipcRenderer || null
export default {
    name: 'ViewBox',
    components: {
        Head,
        mqtt
    },
    provide() {
        return {
            reload: this.reload
        }
    },
    data() {
        return {
            isRouterAlive: true,
            lockObj: null
        }
    },
    computed: {
        config() {
            return this.$store.getters['getTerminalConfig'] || {}
        }
    },
    mounted() {
        this.initIpc()
    },
    beforeDestroy() {
        this.destroyLock()
    },
    methods: {
        reload() {
            const that = this
            that.isRouterAlive = true
            that.$nextTick(function() {
                that.isRouterAlive = true
            })
        },
        initIpc() {
            if (!ipcRenderer) {
                return
            }
            // 远程开柜
            ipcRenderer.removeAllListeners('remote-open-lock')
            ipcRenderer.on('remote-open-lock', (event, data) => {
                console.log('远程开柜信息：', data)
                const lockNum = data.lockNum ? data.lockNum : getGHEvent(data.box, data.door)
                // 远程开柜，先判断柜号位置结果
                const num = cabinetToBoxAndDoor(lockNum)
                console.log('num', num, lockNum)
                if (!num || !num.length) {
                    // tudo 柜号不存在
                    console.log('num', '柜号不存在')
                    this.remoteOpenLockResult({ result: -1, success: true, msg: '柜号不存在', actiontype: 'openlock' })
                    return
                }
                if (this.lockObj) {
                    this.lockObj.handle('openlock', lockNum)
                } else {
                    this.initRemoteOpenLock(() => {
                        this.lockObj.handle('openlock', lockNum)
                    })
                }
            })
            // 下发升级
            ipcRenderer.on('down-upgrade', async() => {
                let obj = {}
                if (this.config.upgradType === 'default') {
                    obj = { code: 200, msg: '终端未对接升级，请重试...' }
                } else if (this.config.upgradType === 'iot') {
                    if (this.$refs.mqttRef) {
                        obj = this.$refs.mqttRef.downUpgrade()
                    } else {
                        obj = { code: 501, msg: '终端异常，请重试...' }
                    }
                }
                ipcRenderer.send('down-upgrade-result', obj)
            })
        },
        // 远程开柜
        initRemoteOpenLock(callback) {
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.remoteOpenLockResult({ result: -1, success: true, msg: data.msg, actiontype: 'openlock' })
                }
            })
            this.lockObj.on('error', (data) => {
                this.remoteOpenLockResult({ result: -1, success: true, msg: data.msg, actiontype: 'openlock' })
            })
            this.lockObj.on('message', (data) => {
                if (data.actionType == 'openlock') {
                    // 开柜结果
                    this.remoteOpenLockResult({ result: data.code == 2 && data.result == 1 ? 1 : -1, success: true, msg: data.code == 7 ? '开门超时' : data.msg, actiontype: 'openlock' })
                }
            })
            this.lockObj.init()
        },
        // 远程开柜结果推送主进程
        remoteOpenLockResult(result) {
            if (ipcRenderer) {
                ipcRenderer.send('remote-open-lock-result', result)
                this.destroyLock()
            }
        },
        // 销毁锁控连接
        destroyLock() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        }
    }
}
</script>

<style lang="less">
#viewBox {
	overflow: auto;
    overflow-x: hidden;
	position: relative;
}
.size {
	width: 100%;
	height: 100%;
}
html,
body {
	.size;
	overflow: auto;
	margin: 0;
	padding: 0;
}
#viewBox {
	.size;
}
</style>
