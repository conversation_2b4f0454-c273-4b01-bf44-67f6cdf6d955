<template>
	<!-- <div class="auth"></div> -->
	 <div>
       <headView  />
	 </div>
</template>

<script>
import MQTT from '@/views/mqtt/index.js'
import NetConfig from './network-config/index'
import IOTConfig from './iot-config/index'
import headView from '@/components/head.vue'
const ipcRenderer = window.electronAPI?.ipcRenderer || null
export default {
	name: 'Auth',
	components:{headView},
	data() {
		return {
			mqtt: null,
			config: {
				iotAddress: '',
				productKey: ''
			},
			dialogObj: null
		}
	},
	mounted() {
		console.log('renderer send start-auth')
		if (!ipcRenderer) {
			return
		}
		ipcRenderer.send('start-auth')
		ipcRenderer.once('get-auth-file', (event, data) => {
			this.config.iotAddress = data.iotAddress
			this.config.productKey = data.productKey
			if (data.iotAddress && data.productKey) {
				setTimeout(() => {
					this.initMqtt()
				}, 2000)
			}
		})
		ipcRenderer.on('open-config-window', (event, arg) => {
			this.dialogObj && this.dialogObj.close()
			const componentMap = {
				network: NetConfig,
				iot: IOTConfig
			}
			this.dialogObj = this.$GxxDialog({
				showHeader: false,
				component: componentMap[arg.type],
				componentParams: arg
			})
			this.dialogObj.on.then((res) => {
				this.sendConfigInfo(res)
				this.dialogObj = null
			})
		})
		ipcRenderer.on('auth-file-error-tip', (event, data) => {
			this.$GxxPrompt.info({
				type: 'confirm',
				showBtn: false,
				autoClose: false,
				message: data.message
			})
		})
		ipcRenderer.on('go-to-loading', (event, data) => {
			this.$router.push('/loader')
		})
	},
	methods: {
		initMqtt() {
			if (!ipcRenderer) {
				return
			}
			const macAddress = ipcRenderer ? ipcRenderer.sendSync('get-mac-address') : ''
			this.mqtt = new MQTT(this.config.iotAddress, { productKey: this.config.productKey, macAddress })
			this.mqtt.on('init', (bool) => {
				console.log(`mqtt 连接${bool ? '成功' : '失败'}...`)
				if (!bool) {
					this.mqtt = null
				}
			})
			this.mqtt.on('message', (topic, data) => {
				console.log('message', topic, data)
				switch (topic) {
					case 'license/get_reply':
						this.sendAuthFile(data.fileUrl)
						break
					default:
						break
				}
			})
			this.mqtt.on('error', (error) => {
				console.log('error', error)
				this.$GxxPrompt.info({
					type: 'warning',
					showBtn: false,
					showCountdown: true,
					timeout: 5000,
					autoClose: true,
					message: '获取授权文件失败，将自动关闭终端！',
					callback: (res) => {
						this.sendAuthFile('')
					}
				})
			})
			this.mqtt.on('receive', (bool) => {
				console.log(`订阅${bool ? '成功' : '失败'}...`)
				// 订阅成功 获取授权文件 校验权限等 页面展示 校验不通过，关闭终端 TODO
				if (bool) {
					this.mqtt.publish('getLicense', { mac: this.mqtt.macAddress, cpuid: this.mqtt.macAddress }, false)
				}
			})
			this.mqtt.on('close', () => {
				console.log('mqtt 连接关闭...')
				this.mqtt = null
			})
			this.mqtt.init()
		},
		sendAuthFile(fileUrl) {
			ipcRenderer && ipcRenderer.send('get-auth-file-result', fileUrl)
		},
		sendConfigInfo(data) {
			ipcRenderer && ipcRenderer.send('open-config-window-result', data)
		}
	},
	beforeDestroy() {
		this.mqtt && this.mqtt.close()
		this.dialogObj && this.dialogObj.close()
	}
}
</script>

<style lang="less" scoped>
.auth {
	width: 100%;
	height: 100%;
	display: flex;
	background: url('~@/assets/images/loader/loadPage.png') no-repeat;
	background-size: 100% 100%;
	position: relative;
	overflow: hidden;
}
</style>
