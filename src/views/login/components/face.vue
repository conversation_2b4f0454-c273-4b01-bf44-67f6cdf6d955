<template>
	<div class="faceCollectWrap">
		<!-- <div class="switchBox">
            <i :class="isOpenLight ? 'switchBtn_open' : 'switchBtn_close'" @click="handleLight"></i>
            <p>{{ isOpenLight ? '关闭补光灯' : '开启补光灯' }}</p>
        </div> -->
		<!-- <div class="closeBtnBox"><span class="closeBtnText">自动退出({{ countdown }}秒)</span></div> -->
		<Header></Header>
		<div class="faceIdentifying">
			<div class="Identifying"></div>
			<canvas id="canvasCamera" class="canvasStyle" style="display: none" width="550" height="450"></canvas>
			<video id="videoBox" ref="faceVideo" autoplay playsinline class="video-style"
				:style="`transform: rotateZ(${videoConfig.rotate}deg) rotateY(${videoConfig.horizontal == 'Y' ? 180 : 0}deg)`"></video>
			<img v-show="isSnap" id="faceImage" alt="" class="imageStyle" />
		</div>
		<i></i>
		<h3 class="IdentifyingTitle driveSB">{{ tipText }}</h3>
		<!-- <div class="loginTips">
			<div class="tipsItem3 tipsItem">
				<div id="cardToId" class="loginByID" @click="handleSwitchLogin">账号密码认证</div>
			</div>
		</div> -->
		<div class="switch-warp">
			<p class="tip">请保持面部放在取景框内</p>
			<!-- <p class="tip">认证出现问题？试试其他认证方式</p> -->
			<i></i>
			<!-- <span class="switch-btn" @click="handleSwitchLogin"></span>
			<span class="switch-btn-text">账号密码认证</span> -->
		</div>
		<div class="operateBox">
			<div class="buttonStyle3" @click="loginOut">退出认证（{{ countdown }}s）</div>
		</div>
	</div>
</template>

<script>
import commonServ from '@/libs/common_serv.js'
import store from '@/stroe'
import light from './mixin/light'
import { setNewToken } from '@/libs/utils'
import { deepClone, getMediaDevices } from '@/utils/normal_util'
import Header from '@/components/head.vue'

export default {
	name: 'FaceCollect',
	mixins: [light],
	components: { Header },
	props: {
		// 退出路由
		fromPath: {
			type: String,
			default: ''
		},
		// 认证后的去处路由
		toPath: {
			type: String,
			default: ''
		},
		handleGetRules: {
			type: Function,
			default: () => { }
		}
	},
	data() {
		return {
			cabinetArr: [],
			modal: false,
			// 认证倒计时
			countdown: 120,
			// 倒计时的定时器对象
			timer: null,
			downCount: 3,
			countdownTime: null,
			// 视频流
			currentStream: null,
			videoConfig: {
				horizontal: '',
				rotate: 0
			},
			videoStyle: {
				width: 460,
				height: 460
			},
			paramsRouter: this.$route,
			tipText: '',
			isSnap: false,
			systemConfig: {},
			imgSrc: "",
			// 
			// '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',
		}
	},
	mounted() {
		this.systemConfig = store.state.config.terminalConfig
		this.countdown = this.serverConfig.loginOutTime || 120
		this.videoConfig.horizontal = this.systemConfig.horizontal || 'X'
		this.videoConfig.rotate = this.systemConfig.rotate || 0
		this.countdownEvent()
		this.openConnect()
		console.log(this.paramsRouter.params, 'this.serverConfig.openFillLightthis.serverConfig.openFillLight')
		// 重新换柜
		this.init()
		if (this.paramsRouter.params && this.paramsRouter.params.type && this.paramsRouter.params.type == 'replay') {
			this.init()
		}
		// this.handleInitLight(() => {
		// 根据配置初始化是否打开补光灯
		if (this.serverConfig.openFillLight == '0') {
			return
		} else {
			console.log(this.serverConfig.openFillLight, 'this.serverConfig.openFillLight')
			this.handleLight()
		}
		// })
	},
	beforeDestroy() {
		this.StopPreview()
		this.timer && clearInterval(this.timer)
		this.timer = null
	},
	methods: {
		// 倒计时
		countdownEvent() {
			this.timer = setInterval(() => {
				this.countdown--
				if (this.countdown == 0) {
					clearInterval(this.timer)
					this.countdown = 0
					this.$router.push(this.fromPath)
				}
			}, 1000)
		},
		init() {
			this.cabinetArr = []
			const params = {
				ip: this.serverConfig.iotAddress || this.serverConfig.ip
			}
			this.$Get(this.AmtCom.getCabinetInfo, params).then((res) => {
				if (res.success) {
					this.cabinetArr = res.data.cabinetList
					this.choosedCabinetData = { num: '' }
					this.cabinetArr.forEach((item) => {
						if (item.gridJsonList && item.gridJsonList.length > 0) {
							item.gridJsonList.forEach((data) => {
								if (data.gridSize == 'M' && data.status == '1') {
									this.$set(this.choosedCabinetData, 'num', data.gridName)
								}
							})
						}
					})
					console.log(this.choosedCabinetData, 'this.choosedCabinetData')
					// this.choosedCabinet = this.choosedCabinetArr
					// this.$store.commit('setCabinet', { cabinet: this.choosedCabinetData })
					// this.$router.push({ name: 'cabinet_put_in', params: { getWplist: this.paramsRouter.params.getWplist } })
				}
			})

		},
		openConnect() {
			if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
				console.log('menumerateDevices is not supported!')
				return
			}
			navigator.mediaDevices.enumerateDevices().then(this.gotDevices).catch(this.handleError)
		},
		// 释放摄像头
		stopVideo() {
			if (this.countdownTime) {
				clearInterval(this.countdownTime)
				this.countdownTime = null
			}
			this.currentStream &&
				this.currentStream.getTracks().forEach(function (track) {
					track.stop()
				})
			this.currentStream = null
		},
		// 暂停流
		pauseVideo() {
			this.countdownTime && clearInterval(this.countdownTime)
			this.countdownTime = null
			this.currentStream && this.$refs.faceVideo.pause()
		},
		// 重新播放
		playVideo(msg) {
			this.currentStream && this.$refs.faceVideo && this.$refs.faceVideo.play()
			this.getPicAndSave(msg)
		},
		// 遍历所有的设备，包括视频和音频设备,找到摄像头
		gotDevices(deviceInfos) {
			const videoSource = []
			// console.log(deviceInfos,'deviceInfosdeviceInfos')
			// this.logger('所有的设备', deviceInfos)
			const cameraName = this.systemConfig.faceCamera || ''
			deviceInfos.forEach((deviceInfo) => {
				if (deviceInfo.kind === 'videoinput' && (!cameraName || (cameraName && deviceInfo.deviceId.indexOf(cameraName) !== -1))) {
					const option = {
						text: deviceInfo.label,
						value: deviceInfo.deviceId
					}
					videoSource.push(option)
				}
			})
			if (videoSource.length > 0) {
				this.play(videoSource[0].value) //柜子有两个摄像头 第一个黑白 第二个彩色
			} else {
				this.handleError('没有摄像头')
			}
		},
		// 获取摄像头进行播放摄像头
		play(deviceId) {
			const constraints = {
				video: {
					height: 480,
					width: 665,
					// frameRate: 15,
					// facingMode: 'enviroment',
					deviceId: deviceId ? { exact: deviceId } : undefined
				},
				audio: false
			}
			navigator.mediaDevices.getUserMedia(constraints).then(this.gotMediaStream).catch(this.handleError)
		},
		// 错误输出
		handleError(err) {
			this.tipText = '无法获取摄像头'
			console.log(err)
		},
		// 异常处理
		handleFaceError(err, retry) {
			if (this.countdownTime) {
				clearInterval(this.countdownTime)
				this.countdownTime = null
			}
			console.log(err, retry)
			this.faceErrorEvent('没有检测到人脸')
		},
		// 播放
		gotMediaStream(stream) {
			if (this.$refs.faceVideo) {
				this.$refs.faceVideo.srcObject = stream
				this.currentStream = stream
			}
			this.getPicAndSave()
		},
		// 拍照
		getPicAndSave(msg) {
			this.downCount = this.$options.data().downCount
			this.countdownTime && clearInterval(this.countdownTime)
			this.countdownTime = null
			this.countdownTime = setInterval(() => {
				this.downCount--
				if (msg) {
					const _msg = msg.replace('。', '')
					this.tipText = _msg + '，' + this.downCount + '秒后重试'
				}
				if (this.downCount <= 0) {
					this.downCount = 0
					clearInterval(this.countdownTime)
					this.countdownTime = null
					this.tipText = ''
					this.snapPhoto()
				}
			}, 1000)
		},
		snapPhoto() {
			try {
				const canvas = commonServ.handleCameraCof(
					{
						height: this.videoStyle.height,
						width: this.videoStyle.width
					},
					this.videoConfig,
					'canvasCamera',
					'videoBox'
				)
				const base64Code = canvas.toDataURL('image/jpeg')
				const photoshopMode = this.systemConfig?.photoshopMode || -1
				this.pauseVideo()
				this.tipText = '正在认证身份，请稍后...'
				if (photoshopMode != -1) {
					// 调C++SDK库，进行人脸抠图
					this.photoshop(base64Code)
				} else {
					const faceImage = document.getElementById('faceImage')
					faceImage.src = base64Code
					this.isSnap = true
					const base64 = base64Code.replace('data:image/jpeg;base64,', '')
					this.handleFaceLogin(base64)
				}
			} catch (error) {
				console.log(error)
				this.StopPreview()
			}
		},
		// 抠图
		photoshop(base64Code) {
			try {
				const ipcRenderer = window.electronAPI.ipcRenderer
				const faceResult = ipcRenderer.sendSync('face_recognition_path', { base64: base64Code, width: this.videoStyle.width, height: this.videoStyle.height })
				if (faceResult) {
					// 处理后的绘制点
					const afterDrawX = faceResult.x - faceResult.width / 2
					const afterDrawY = faceResult.y - faceResult.height / 2
					// 最终实际的绘制点
					const drawX = afterDrawX <= 0 ? 0 : afterDrawX
					const drawY = afterDrawY <= 0 ? 0 : afterDrawY
					// 最终实际的区域大小
					const drawWidth = faceResult.width * 2
					const drawHeight = faceResult.height * 2
					const canvas = document.createElement('canvas')
					canvas.width = drawWidth
					canvas.height = drawHeight
					canvas.getContext('2d').drawImage(document.getElementById('canvasCamera'), drawX, drawY, drawWidth, drawHeight, 0, 0, canvas.width, canvas.height)
					const faceImage = document.getElementById('faceImage')
					faceImage.src = canvas.toDataURL('image/jpeg')
					this.isSnap = true
					const base64 = faceImage.src.replace('data:image/jpeg;base64,', '')
					this.handleFaceLogin(base64)
				} else {
					const tt = setTimeout(() => {
						clearTimeout(tt)
						this.handleFaceError()
					}, 2 * 1000)
				}
			} catch (e) {
				this.faceErrorEvent('人脸检测异常')
			}
		},
		// 统筹保管员|暂存模式下的登录入口
		handleFaceLogin(base64) {
			const loginMap = {
				0: this.faceAction_bg,
				1: this.faceAction
			}
			// 先判断物管柜配置使用的模式
			const patternTypeOfCabinet = this.serverConfig.patternType
			if (loginMap[patternTypeOfCabinet]) {
				loginMap[patternTypeOfCabinet](base64)
			} else {
				// 因5033版本兼容5011的保管服务，物管柜配置是没有返回patternType的，所以再从本地终端配置获取
				const patternTypeOfTerminal = this.systemConfig.patternType
				loginMap[patternTypeOfTerminal](base64)
			}
		},
		// 暂存模式（bsp）登录接口
		faceAction(photo) {
			this.tipType = 0
			const params = {
				grant_type: 'password',
				password: '',
				client_id: 'user_client',
				client_secret: 'user_client',
				auth_type: 'face',
				scope: 'trust',
				app_mark: 'sacw',
				faceCode: this.systemConfig.bspFaceLoginCode,
				faceImage: photo
			}
			this.$Post(this.BspApi.login_url, params).then(
				(resp) => {
					console.log('resp', resp)
					if (resp.code == 400) {
						this.faceErrorEvent(resp.msg)
						return
					}
					window.frist_in = 0
					this.$store.commit('setUser', resp)
					setNewToken(resp.access_token)
					return
					this.$router.push(this.toPath)
				},
				() => {
					this.faceErrorEvent('人脸服务异常')
				}
			)
		},
		// 保管模式登录接口
		faceAction_bg(photo) {
			this.tipType = 0
			// this.showMsgBoxText = '识别中，请稍后'
			var params = {
				// centerId: this.serverConfig.centerId,
				// faceImg: photo
				grant_type: 'password',
				password: '',
				client_id: 'user_client',
				client_secret: 'user_client',
				auth_type: 'face',
				scope: 'trust',
				app_mark: 'amt',
				faceCode: this.systemConfig.bspFaceLoginCode,
				faceImage: this.imgSrc //photo
			}
			this.$Post(this.BspApi.login_url, params).then(
				(resp) => {
					console.log('resp', resp)
					if (resp.code == 400) {
						this.faceErrorEvent(resp.msg)
						return
					}
					window.frist_in = 0
					this.$store.commit('setUser', resp)
					setNewToken(resp.access_token)
					//申请换柜
					if (this.paramsRouter.params && this.paramsRouter.params.type && this.paramsRouter.params.type == 'replay') {
						this.$store.commit('setCabinet', { cabinet: this.choosedCabinetData })
						this.$router.push({ name: 'cabinet_put_in', params: { getWplist: this.paramsRouter.params.getWplist } })
					} else {
						// 正常识别成功后的跳转
						return
					  this.$router.push(this.toPath)
					}

				},
				() => {
					this.faceErrorEvent('人脸服务异常')
				}
			)
			this.$Post(this.BspApi.login_url, params).then(
				//this.FaceApi.BUS_FACE
				(resp) => {
					if (resp.success) {
						if (resp.access_token && resp.data) {
							this.headlFaceData(resp)
						} else if (!photo && !resp) {
							this.faceErrorEvent('认证成功，未识别到人员')
						} else {
							// 比对失败
							this.faceErrorEvent('认证失败')
						}
					} else {
						this.faceErrorEvent('服务器认证失败')
					}
				},
				() => {
					this.faceErrorEvent('人脸服务异常')
				}
			)
		},
		/**
		 * 人脸识别到的数据处理
		 * @param {object} data shuj
		 * @param {string} data.idCard
		 * @param {string} data.name
		 * @param {string} data.loginId
		 * @param {string} data.orgCode
		 * @param {string} data.orgName
		 * @param {string} data.centerId
		 * @param {string} data.isAdmin
		 */
		headlFaceData(data) {
			const params3 = {
				centerId: this.serverConfig.centerId,
				type: '03',
				idCard: data.idCard
			}
			this.$Post(this.PymApi.getAdminInfo, params3).then((res) => {
				if (res.success) {
					localStorage.setItem('userInfo', JSON.stringify(res.admin))
					this.$store.commit('setUser', res.admin)
					window.frist_in = 0
					this.$router.push(this.toPath)
				} else {
					this.faceErrorEvent(res.msg)
				}
			})
		},
		faceErrorEvent(tipVal) {
			// 人脸失败处理方
			this.isSnap = false
			this.playVideo(tipVal)
		},
		// //关闭摄像头
		StopPreview() {
			this.currentStream && this.currentStream.getTracks()[0].stop()
			this.countdownTime && clearInterval(this.countdownTime)
			this.countdownTime = null
		},
		// 账号登录
		handleSwitchLogin() {
			this.$GxxMessage({ message: '功能开发中！', type: 'warning' })
			return
			this.pauseVideo()
			this.StopPreview()
			this.timer && clearInterval(this.timer)
			this.timer = null
			this.$emit('switchLogin', 'account')
		},
		loginOut() {
			// 退出注册
			this.$router.push(this.fromPath)
		}
	}
}
</script>

<style lang="css">
.AuthenticateNow {
	width: 798px;
	height: 120px;
	line-height: 120px;
	text-align: center;
	background: #337eff;
	border-radius: 16px;
	box-shadow: 0px 12px 20px 0px rgba(51, 126, 255, 0.3);
	color: #fff;
	font-size: 48px;
	margin: 0 auto 50px;
}
</style>
<style lang="less" scoped>
@assets: '~@/assets/images';

.faceCollectWrap {
	width: 100%;
	height: 100%;
	background: #ebf5ff; // rgba(24, 22, 67, 0.9);
	position: fixed;
	top: 0;
	left: 0;
	overflow: auto;
}

.video-style {
	float: left;
	object-fit: cover;
	width: 550px;
	height: 450px;

	&.horizontal {
		transform: rotateY(180deg);
	}

	&.rotate {
		transform: rotate(270deg);
	}

	&.horizontal.rotate {
		transform: rotate(270deg) rotateX(180deg);
	}
}

.closeBtnBox {
	width: 330px;
	border-radius: 8px;
	border: solid 2px #4f5eff;
	color: #ffffff;
	text-align: center;
	line-height: 96px;
	position: absolute;
	right: 50px;
	top: 50px;
	height: 96px;
	text-align: center;
}

.closeBtnText {
	font-size: 36px;
	color: #fff;
}

.operateBox {
	margin-top: 85px;
	margin-bottom: 100px;
}

.tipsItem {
	display: flex;
	justify-content: center;
	align-items: center;
}

.tipsItem3 {
	.loginByID {
		width: 400px;
		height: 96px;
		background-color: #252357;
		border-radius: 48px;
		margin: auto;
		margin-top: 100px;
		color: #669eff;
		font-size: 36px;
		text-align: center;
		line-height: 96px;
	}

	.loginType {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin: 0 102px;

		.fingerprint {
			display: inline-block;
			width: 128px;
			height: 128px;
			background: url('@{assets}/fingerIcon.png') no-repeat;
			background-size: 100% 100%;
		}

		.accountNumber {
			display: inline-block;
			width: 128px;
			height: 128px;
			background: url('@{assets}/accountIcon.png') no-repeat;
			background-size: 100% 100%;
		}

		span {
			display: inline-block;
			width: 100%;
			font-size: 28px;
			text-align: center;
			color: #669eff;
			line-height: 40px;
			margin-top: 19px;
		}
	}
}

.switch-warp {
	display: flex;
	flex-direction: column;
	align-items: center;

	.tip {
		color: #669eff;
		font-size: 28px;
		margin-top: 44px;
		line-height: 1;
	}

	i {
		display: inline-block;
		width: 32px;
		height: 30px;
		margin-top: 18px;
		background-size: 100% 100%;
		transform: rotate(180deg);
		background-image: url('@{assets}/temp/arrow-down.png');
	}

	.switch-btn {
		display: inline-block;
		width: 128px;
		height: 128px;
		background-size: 100% 100%;
		background-image: url('@{assets}/temp/switch-account-icon.png');
		margin-top: 25px;
	}

	.switch-btn-text {
		font-size: 28px;
		color: #669eff;
		margin-top: 15px;
	}
}

.IdentifyingTitle {
	font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
	font-weight: bold;
	font-size: 42px;
	color: #2b3346;
}
</style>
