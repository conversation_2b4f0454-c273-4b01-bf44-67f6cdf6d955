<template>
    <div class="choose-cabinet-warp">
        <Step :current-step="4"></Step>
        <div class="choose-cabinet">
            <Cabinet v-if="loaded" :choosed-cabinet="choosedCabinet" :cabinet-detail="cabinetDetail" @choosed="handleChoosed"></Cabinet>
        </div>
        <div class="tool">
            <span class="prev" @click="handlePrev">上一步</span>
            <span class="next" @click="handleNext">确认开柜</span>
        </div>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import { getToken } from '@/libs/utils'
import Cabinet from './cabinet_scroll_view'
// import confirmItemDialog from '../cabinet_put_in/confirm_item_dialog'
export default {
    components: {
        Step,
        Cabinet
    },
    data() {
        return {
            // 柜子面数信息
            cabinetSides: [],
            // 柜子面-结构信息
            cabinetDetail: [],
            // 柜子储藏信息
            cabinetAssign: [],
            // 选中的柜子
            choosedCabinet: {},
            loaded: false,
            isConfrim: false
        }
    },
    mounted() {
        // this.choosedCabinet = this.$store.getters.getCabinet || {}
        console.log(this.$route.params.getWplist, '==========带着物品去选柜===============')
        // this.getCabinetGh()
    },
    methods: {
        handlePrev() {
            console.log(this.$store.getters.getCabinet, '==返回上一级的柜子==')
            let statusCode = -1
            if (this.$route.params.notDeposit) {
                statusCode = 3
            }
            this.$router.push({ name: this.$route.params.fromName, params: { statusCode }})
        },
        handleNext() {
            if (!this.choosedCabinet.num) {
                return this.$GxxMessage({ message: '请选择存放位置！', type: 'warning'})
            }
            this.$store.commit('setCabinet', { cabinet: this.choosedCabinet })
            // this.$GxxDialog({
            //     showHeader: false,
            //     showHeaderClose: false,
            //     showFooter: false,
            //     component: confirmItemDialog,
            //     componentParams: this.$route.params.getWplist
            // }).on.then((ret) => {
            // })
            this.$router.push({ name: 'cabinet_put_in', params: { getWplist: this.$route.params.getWplist }})
        },
        // 选中柜子回调
        handleChoosed(cabinet) {
            this.choosedCabinet = cabinet
        },
        // 获取物品柜柜号
        getCabinetGh() {
            this.$Post(this.PymApi.getCabinetByCabinetCode, {
                access_token: getToken(),
                centerId: this.serverConfig.centerId,
                cabinetCode: this.serverConfig.cabinetCode
            }).then((resGh) => {
                if (!resGh.success || !resGh.data) {
                    return
                }
                this.$Post(this.PymApi.getSideByCabinetId, {
                    access_token: getToken(),
                    centerId: this.serverConfig.centerId,
                    cabinetId: resGh.data && resGh.data.id
                }).then((res) => {
                    if (res.success && res.data && res.data.length) {
                        this.cabinetSides = res.data
                        // 遍历请求柜子结构信息
                        const CabinetDetail = this.cabinetSides.map((sideData) => {
                            return this.getCabinetDetails(sideData.id)
                        })
                        Promise.all(CabinetDetail).then((res) => {
                            this.cabinetDetail = res.map((item, i) => {
                                item.sideCode = this.cabinetSides[i].sideCode
                                item.sideId = this.cabinetSides[i].id
                                return item
                            })
                            this.loaded = true
                            // 本地缓存一份下个节点使用
                            localStorage.setItem('cabinetDetail', JSON.stringify(this.cabinetDetail))
                        })
                    }
                })
            })
        },
        // 获取物品柜结构信息
        getCabinetDetails(sideId) {
            if (!sideId) return false
            return new Promise((resolve, reject) => {
                const params = {
                    access_token: getToken(),
                    centerId: this.serverConfig.centerId,
                    sideId
                }
                this.$Post(this.PymApi.getCabinetDetail, params)
                    .then((res) => {
                        resolve(res)
                    })
                    .catch((err) => {
                        reject(err)
                    })
            })
        }
    }
}
</script>

<style lang="less" scoped>
.choose-cabinet-warp {
	width: 100%;
	margin: 0 auto;
	.choose-cabinet {
		min-height: 1150px;
	}

	.tool {
		display: flex;
		justify-content: center;
		span {
			display: inline-block;
			width: 320px;
			height: 96px;
			font-size: 36px;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 8px;
			margin: 10px 60px 40px;
			cursor: pointer;
			&.prev {
				color: #337eff;
				border: 2px solid #337eff;
				background-color: #fff;
			}
			&.next {
				color: #fff;
				background-color: #337eff;
			}
			&.nochoose {
				background-color: #9bbdfa;
			}
			&.choosed {
				background-color: #337eff;
			}
		}
	}
}
</style>
