<template>
    <div class="finish-warp">
        <Step ref="Step" :current-step="5" :stop-count-down="true" :network="network" @backHome="returnHome"></Step>
        <headNav :show-box-index="[false, false, true]" :sysTitle="sysTitle"></headNav>  
        <div class="cabinet-deal-status">
            <div v-if="statusCode == 1" class="deal-status opening">
                <i></i>
                <p class="title">柜门开启中</p>
                <p class="tip">
                    <span>{{ currentCabinet.num }}</span>
                    号柜门开柜中，请稍候···
                </p>
            </div>
            <div v-if="statusCode == 2" class="deal-status opened">
                <i></i>
                <p class="title">柜门开启</p>
                <p v-if="!network" class="tip">网络已断开,请取出已存入物品！</p>
                <p v-else class="tip">
                    <span>{{ currentCabinet.num }}</span>
                    号柜门已开启，存好物品后
                    <span>请关好柜门</span>
                </p>
            </div>
            <div v-if="statusCode == 3" class="deal-status closed">
                <i></i>
                <p class="title">柜门已关闭</p>
                <p class="tip">
                    <span>{{ currentCabinet.num }}</span>
                    号柜门已关闭
                    <!-- ，请确认所有物品已放入 -->
                </p>
            </div>
            <!-- <div v-if="statusCode == 0" class="deal-status error">
                <i></i>
                <p class="title">柜门开启失败</p>
                <p class="tip">
                    <span>{{ currentCabinet.num }}</span>
                    号柜门开柜失败，请点击异常处理
                </p>
            </div> -->
        </div>
        <door   :currentCabinetNum="currentCabinet.num" />
        <!-- <Cabinet v-if="loaded" :cabinet-detail="cabinetDetail" :current-cabinet-num="currentCabinet.num"></Cabinet> -->
        <p v-if="statusCode == 1" class="open-tip">正在开门中，请您留意！</p>

        <!-- <p v-if="statusCode == 2 || statusCode == 0" class="open-tip">柜门未正常开启？你可以点击【异常处理】进行处置</p>
        <div v-if="statusCode == 2 || statusCode == 0" class="tool">
            <span class="fault-btn" @click="handleShowErrorModal">异常处理</span>
        </div> -->
       <p v-if="statusCode == 2 || statusCode == 0" class="open-tip">
          <span class="yjgbDoor">我已存好物品关闭柜门</span>
       </p>
        <div v-if="statusCode == 2 || statusCode == 0" class="tool-gz">
           柜门不够大？<span class="change-btn" @click="handleReplayChooseCabinet" style="color: blue;">申请换柜</span>
        </div>
        <!-- <p v-if="statusCode == 3" class="open-tip closed-tip">物品太多放不下？可点击【有物品未放入】将剩余物品放进新柜子</p> -->
        <div v-if="statusCode == 3" class="tool tool-closed">
            <!-- <span class="replay-btn" @click="handleReChooseWp">有物品未放入</span> -->
            <span class="back-btn" @click="returnHome">返回首页({{ returnHomeTime + 'S' }})</span>
        </div>
        <!-- 错误提示 -->
        <Modal v-model="tipErrorModal" :closable="false" :mask-closable="false" footer-hide>
            <tip-error-modal v-if="tipErrorModal" :open-count="errorCount" @close="tipErrorModal = false" @replay="handleReplayChooseCabinet" @keepOn="handleReopen" @error="handleError"></tip-error-modal>
        </Modal>
        <!-- 选择未存入物品 -->
        <Drawer v-model="drawer" :styles="{ padding: 0, overflow: 'inherit' }" placement="bottom" height="80" :closable="false">
            <choosed-wp v-if="drawer" :current-cabinet="currentCabinet" @close="drawer = false"></choosed-wp>
        </Drawer>
    </div>
</template>

<script>
import Step from '_c/step.vue'
import Cabinet from './cabinet_all_view'
import TipErrorModal from './tip_error_modal'
import ChoosedWp from './choosed_wp'
import { voiceBroadcast } from '@/libs/utils'
import { getGHEvent } from '@/libs/lock/util'
import Lock from '@/libs/lock/index'
import { removeNewToken } from '@/libs/utils'
import confirmItemDialog from './confirm_item_dialog'
import headNav from '_c/headNav'
import door from "@/components/door.vue"

export default {
    components: {
        Step,
        Cabinet,
        ChoosedWp,
        TipErrorModal,
        headNav,door
    },
    data() {
        return {
            sysTitle:'',
            loaded: false,
            isSaveSuccess: false, // 是否存入柜子成功
            // 柜门状态 已关闭：3、开启中：1、已开启：2
            statusCode: 1,
            errorCount: 0,
            tipErrorModal: false,
            cabinetDetail: [],
            drawer: false,
            retryTimes: 3,
            terminalConfig: {},
            lockObj: null,
            network: true,
            isUpload: false, // 是否数据已上传
            errorUpload: false,
            dialogObj: null,
            returnHomeTime: 120,
            returnHomeTimer: null // 返回首页定时器
        }
    },
    computed: {
        currentCabinet() {
            console.log(this.$store.getters.getCabinet,'this.$store.getters.getCabinet',this.$store.getters)
            return this.$store.getters.getCabinet
        }
    },
    watch: {
        // 检查状态已为关门状态，触发自动上传
        statusCode(code) {
            if (code == 3) {
                this.$refs.Step && this.$refs.Step.handleActionCountDown()
            }
        }
    },
    created() {
        // this.terminalConfig = this.$store.getters.getTerminalConfig
        const cabinetDetail = localStorage.getItem('cabinetDetail')
        this.cabinetDetail = JSON.parse(cabinetDetail || '[]')
        this.loaded = true
        this.returnHomeTime = this.serverConfig.loginOutTime || 120
    },
    beforeDestroy() {
        this.destroyedLock()
        voiceBroadcast(false)
        this.clearReturnHomeTimer()
        this.dialogObj && this.dialogObj.close && this.dialogObj.close()
        this.dialogObj = null
    },
    mounted() {
        if (this.$route.params.statusCode != -1) {
            this.statusCode = this.$route.params.statusCode
        } else {
            this.statusCode = 1
        }
        console.log(this.$route.params, '==进入开关柜的参数===')
        // this.openLock()
        // 清空未知定时器
        this.$refs.Step && this.$refs.Step.handleClearAll()
        this.logger({ title: '暂存的存入开柜的柜号', value: this.currentCabinet },this.statusCode ,'this.statusCode', this.$store.getters.getCabinet)
        if (this.statusCode != 3 && this.$store.getters.getCabinet) {
            this.initLock(() => {
                this.openLock()
            })
        }
        if (this.statusCode == 3) {
            this.startReturnHomeTimer()
        }
    },
    methods: {
        initLock(callback) {
            if (this.lockObj) {
                callback && callback()
                return
            }
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                this.logger({ title: 'open-存入查锁记录-串口', value: data })
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.errorCabinet('连接锁控服务失败！', callback)
                }
            })
            this.lockObj.on('error', (data) => {
                this.logger({ title: 'error-存入查锁记录-串口', value: data })
                this.errorCabinet(data.code == 1 ? '连接锁控服务失败！' : data.msg, callback)
            })
            this.lockObj.on('message', (data) => {
                this.logger({ title: 'message-存入查锁记录-串口', value: data })
                const { result, actionType, code, box, door } = data
                const gh = getGHEvent(box, door)
                if (actionType == 'openlock') {
                    // result：1：门开启、0：门关闭
                    this.logger({ title: '存入开锁记录', value: JSON.stringify(data) })
                    if (code == 2) {
                        if (result == 1) {
                            // 开门成功
                            this.statusCode = 2
                            setTimeout(this.checkLock, 2000)
                            this.voiceBroadcastSuccessOpen(gh)
                        } else {
                            // 开门失败
                            this.retryTimes--
                            // 重试
                            if (this.retryTimes) {
                                this.openDoor(gh)
                            } else {
                                // 重试三次失败
                                this.statusCode = 0
                                this.handleShowErrorModal()
                                this.voiceBroadcastFailedOpen()
                            }
                        }
                    } else {
                        this.statusCode = 0
                        this.handleShowErrorModal()
                        this.voiceBroadcastFailedOpen()
                    }
                }
                if (actionType == 'checklock') {
                    this.logger({ title: '存入查锁记录', value: JSON.stringify(data) })
                    if (code == 2) {
                        // result：1：门开启、0：门关闭
                        if (result == 1) {
                            this.statusCode = 2
                            this.checkLock()
                        }
                        if (result == 0) {
                            this.statusCode = 3
                            this.isSaveSuccess = true
                            // this.putInArchiveOperate(gh)
                            this.voiceBroadcastClosed(gh)
                            // this.confirmItemStorages()
                        }
                    } else {
                        this.checkLock()
                    }
                }
            })
            this.lockObj.init()
        },
        destroyedLock() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        },
        openLock() {
            this.retryTimes = 3
            this.statusCode = 1
            console.log(this.currentCabinet,this.$route.params.getWplist,'this.currentCabinetthis.currentCabinet')
            this.openDoor(this.currentCabinet.num)
        },
        // 每次开门成功后-开始定时查询柜门开启状态
        checkLock() {
            this.logger({ title: '发送查锁指令' })
            this.lockObj && this.lockObj.handle('checklock', this.currentCabinet.num)
        },
        openDoor(curCabNum) {
            this.logger({ title: '发送开锁指令' ,num:curCabNum})
            this.lockObj && this.lockObj.handle('openlock', curCabNum)
        },
        // 开门成功
        voiceBroadcastSuccessOpen(curCabNum) {
            const tips = `请将物品存到${curCabNum}号柜子`
            voiceBroadcast(true, tips, 'open_putIn')
        },
        // 开门失败
        voiceBroadcastFailedOpen() {
            const tips = `开锁失败,请联系管理员`
            voiceBroadcast(true, tips, 'openLockFailed')
        },
        // 关门（存入成功）
        voiceBroadcastClosed(curCabNum) {
            const tips = `${curCabNum}号柜子已关闭，物品存入成功`  //，请确认您的下一步操作
            voiceBroadcast(true, tips, 'putInSuccess')
             this.putInArchiveOperate(curCabNum)
        },
               // 存入物品操作
        putInArchiveOperate(curCabNum) {
            const that = this
            const params = {
                    ip: this.serverConfig.iotAddress  || this.serverConfig.ip,
                    jgrybm:  this.serverConfig.jgrybm,
                    storageLocation: curCabNum,
                    wpbhList:this.$route.params.getWplist && this.$route.params.getWplist.length>0?JSON.stringify(this.$route.params.getWplist):''  //this.$store.getters.getWplist //[]
            }
            // 保存存入物品信息
            this.$Post_Json(this.AmtCom.depositItemsUpdateStatus, params).then((res) => {
                // 保存取出信息成功
                if (res.success && res.data) {
                    // 3:柜门已关闭
                    // this.statusCode = 3
                    // this.voiceBroadcastClosed(curCabNum) 
                    // that.wpList.forEach((item, index) => {
                    //     if (item.id == that.activeWpObj.id) {
                    //         that.wpList.splice(index, 1)
                    //     }
                    // })
                    setTimeout(() => {
                        that.startReturnHomeTimer() 
                    }, 2000)
                } else {
                    voiceBroadcast(true, '更新数据失败，请联系管理员', 'updateErr')
                    that.ModalConfirm('重试', '返回首页', 'tipsIcon2', '更新数据失败，请联系管理员', (val) => {
                        if (val) {
                            setTimeout(() => {
                                // that.takeOutArchiveOperate()
                            }, 1500)
                        } else {
                            that.$router.replace('/home')
                        }
                    })
                }
            })
        },
        // 重新开柜
        handleReopen() {
            this.tipErrorModal = false
            this.openLock()
        },
        handleShowErrorModal() {
            this.errorCount++
            this.tipErrorModal = true
        },
        // 重新选柜
        handleReplayChooseCabinet() {
            // this.$store.commit('removeCabinet')
            // this.$router.push({ name: 'choose_cabinet', params: { fromName: 'cabinet_put_in', getWplist: this.$route.params.getWplist }})
            this.$router.push({ name: 'faceLogin', params: { fromName: 'cabinet_put_in', getWplist: this.$route.params.getWplist,type:'replay' }})
        },
        // 触发确认物品已入库操作
        confirmItemStorages() {
            this.dialogObj && this.dialogObj.close && this.dialogObj.close()
            this.dialogObj = this.$GxxDialog({
                showHeader: false,
                showHeaderClose: false,
                showFooter: false,
                component: confirmItemDialog,
                componentParams: this.$route.params.getWplist
            })
            this.dialogObj.on.then((ret) => {
                if (ret.type == 'cancel') {
                    this.openDoor(ret.data)
                } else if (ret.type == 'confirm') {
                    this.startReturnHomeTimer()
                }
            })
        },
        // 开启返回首页倒计时
        startReturnHomeTimer() {
            this.clearReturnHomeTimer()
            this.returnHomeTimer = setInterval(() => {
                if (this.returnHomeTime <= 0) {
                    this.returnHome()
                } else {
                    this.returnHomeTime--
                }
            }, 1000)
        },
        // 清除返回首页倒计时
        clearReturnHomeTimer() {
            this.returnHomeTimer && clearInterval(this.returnHomeTimer)
            this.returnHomeTimer = null
        },
        // 触发有物品未放入按钮弹窗
        handleReChooseWp() {
            console.log(this.$store.getters.getWplist,this.$route.params.getWplist, '======触发有物品未放入===')
            this.drawer = true
        },
        async handleError() {
            this.handleFaultCabinet(() => {
                this.tipErrorModal = false
                this.returnHome()
            })
        },
        async handleFaultCabinet(callback) {
            // 故障上报触发，需通知到物品保存流程（当前物品存入失败）
            this.errorUpload = true
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId,
                gh: this.currentCabinet.num,
                gzyy: this.currentCabinet.num + '不能开柜'
            }
            this.$Post(this.PymApi.saveCabinetFault, params).then((res) => {
                this.logger({ title: '存入-柜子故障', value: params })
                callback && callback()
            })
        },
        // 智能柜异常处理
        errorCabinet(txt, callback) {
            this.destroyedLock()
            this.ModalConfirm('重新连接', '返回首页', 'tipsIcon2', txt, (flag) => {
                if (flag) {
                    this.initLock(callback)
                } else {
                    this.returnHome()
                }
            })
        },
        returnHome() {
            this.$store.commit('removeUser')
            removeNewToken()
            this.$router.push({ name: 'home' })
        }
    }
}
</script>

<style lang="less">
@assets: '~@/assets/images/temp';
.tool-gz{
    color: #5779B3;
    font-size: 36px;
    text-align: center;
}
.yjgbDoor{
   display: inline-block;
   background: #3377ff;
   padding:  16px;
   font-size: 26px;
   color:#fff;
   line-height: 50px;

}
.finish-warp {
	padding-bottom: 20px;
	.cabinet-deal-status {
		padding-bottom: 30px;
		.deal-status {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			i {
				display: inline-block;
				width: 120px;
				height: 120px;
				background-size: 100% 100%;
				background-image: url('@{assets}/opening-icon.png');
				margin-top: 32px;
			}
			.title {
				font-size: 36px;
				color: #56CD45;//#33a0ff;
				font-weight: bold;
				line-height: 1;
				margin-top: 20px;
			}
			.tip {
				font-size: 40px;
				color: #5779b3;
				margin-top: 30px;
			}
			span {
				font-size: 40px;
				color: #3377ff;
				font-weight: bold;
			}
			&.opened {
				i {
					background-image: url('@{assets}/opened-icon.png');
				}
				.title {
					color: #56cd45;
				}
			}
			&.error {
				i {
					background-image: url('@{assets}/error-icon.png');
				}
				.title {
					color: #f31d68;
				}
			}
		}
	}
	.open-tip {
		font-size: 26px;
		color: #191e3b;
		text-align: center;
		line-height: 1;
		margin-top: 40px;
		&.closed-tip {
			height: 60px;
			width: 80%;
			margin: 20px auto 0;
			background-color: #fee3e6;
			border-radius: 8px;
			color: #f84558;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	.tool {
		display: flex;
		justify-content: center;
		margin-top: 20px;
		.fault-btn {
			width: 264px;
			height: 64px;
			background: #f26b6b;
			border-radius: 32px;
			font-size: 24px;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
		}
		&.tool-closed {
			span {
				width: 320px;
				height: 96px;
				border-radius: 8px;
				font-size: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 60px;
				cursor: pointer;
			}
			.replay-btn {
				border: 2px solid #f84558;
				color: #f84558;
				background-color: #fff;
			}
			.back-btn {
				background-color: #337eff;
				color: #fff;
			}
		}
	}
}
</style>
