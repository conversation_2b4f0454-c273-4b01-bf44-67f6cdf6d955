<template>
	<div id="home">
		<p class="title-app">{{ title }}</p>
		
		<div class="count" >  
			<!--  //@click="$router.push('/FileInventory')" -->
			<ul class="type-wrap">
				<li v-for="(item, index) in typeData" :key="index" class="type-child">
					<p class="type-flex">
						<img :src="item.icon" style="width: 89px" />
						<span class="type-wrap-title">{{ item.title }}</span>
					</p>
					<span class="type-wrap-value">{{ item.value }}</span>
				</li>
			</ul>
			<!-- <ul class="number">
				<li>{{ cabinetTotal.total }}</li>
				<li>{{ cabinetTotal.assign }}</li>
				<li v-if="serverConfig.serverVersion != 3">{{ cabinetTotal.free }}</li>
				<li>{{ cabinetTotal.fault }}</li>
			</ul>
			<ul class="title">
				<li>总量</li>
				<li>占用</li>
				<li v-if="serverConfig.serverVersion != 3">空闲</li>
				<li>故障</li>
			</ul> -->
		</div>
		<div class="TopBox">
			<div class="selectionType">
				<div class="vertical">
					<div @click="putInAction">存入物品</div>
					<div @click="takeOutAction">取出物品</div>
				</div>
			</div>
		</div>
		<p v-if="systemConfig.copyright" class="copyright">{{ systemConfig.copyright }}</p>
	</div>
</template>

<script>
import { setToken } from '@/libs/utils.js'
import store from '@/stroe'
import { getGHEvent, cabinetToBoxAndDoor } from '@/libs/lock/util'
export default {
	data() {
		return {
			systemConfig: {},
			// imgSrc:'data:image/jpeg;base64,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 odmZVMgUgMdh7VJYbFQgyDS+XE1OnZOZNRiYeM/Smsl7CN0YZHpUTkfUUPKtxlf8AUN67gtL6Td7yTlPku1WfiJaeg1d6uiqQTtVbdoEi31vJzNyoF8I9cVNW0dWYuZJW/wDU+RUd4w0dZrGTC7JGxPKMHYUjJOyvBBPRnjXbvlICMCN/6UGs5jct0yeprjiy75JZDGcBGPKD1xS3BVpPfWU94AXCuIyQOnnRObjjs8knkoeXzJb2byOQMKT+1VVcSGedpebJZid6nXHlzLZ2TxjZupB9KrFL8q4ZnyDvQwm1sDyIRnSJLZwfyTz4OemKYXWkzSqSuennTnT76OZQEfAG2KI/ERKhDMDQ8rdFi8ROCaK81DS5o2beo1qCOhI2yKnvE2q20CmONVDnrUBuma4ctzbE0/HJpUQ+XjhB1EYLKQ2GJpQANvnI6711JYXAUScrcpzvjakVDocMDTFJMjatbHKcn6aLaciysowdhQmNMDOalHC+nvfXSIi5A8/rSMjvodHG32ae/DXpkN1bTz3dpHIUjUqWUEDdtt/pUr1BRJ2p39kigIlvbOVVcDdfF/al+wThyax0peVZGjuFJywIwcnGKUubUJ2yanC4dWSG3wM4DARefyzQY02tjHC0Wdo9gYo1AAAAxgVIorQFBsaQ0mzJhjB3IUb0ejgwgAoJws8q9kda0PPgjNErbTyVBIxS/wAMjScwTNFbazyo2pzpmwbb2MPg8J0oPrOmd7bzIUyGQjFTIWeRuN6SudN7xWU4IxS5RvRRCSTPP7tF0m4seIL6zbnHdykcp6j/AJtQrg7jLXIccNcO2Uc0k0pfLLnyAJ9q0d2z9j012t7xFZnlER7x9s5AXfpvVR9ivZtcyanr98pJOmW8bjC9Vdmyf2FMi1x4sCUf7E4+yI8VcM8R6jO38Rug7tnZBsPtVfXWgPBObcybqcVfGs2OqXU0rxr3K5KhiNhj39apV5Z5ruQyp4gxBxnqDS4NyPeVGEGr7YzS0uLBgQ+c9Kf3N2GtumGA9etONTt3l0i4lhyJIoy4+gyf2qBz3GqYBeR1XqN+vtTNOgsHkyxXGXTDQ0OXV5ueRth5+1Lpw9pls4E8SyBfI+Zp1pd0YtOifm5mZfERTG5knNz3nK3KTvmnKConzKvk/YX+B0ia1WJYAiLuq+lQ7V7O2gvGiWAAE7YHWi8t5KZAsYOKRkQS3sLSpzbgYPnQLTo8sTkkwLqOnWyTxLbvzcygt5YPpV6fh/7L7riEtqZgLIsndhSOvqaqziDQHs9SEDoqHkR/Cc5/4RXov2Sdm9nwpwjokVtEQ0lnFM+2PGyAtSZxcnUQ+MoyfIecG8GDQLVbZQSF2qtNSib/AK060VQ+CGIf/GgrRUFkyYONs1QdzEJe2viLAyYxGDk/6EH9jTFGSFSftFraHaOLaMsDzMoJ+dHVtWCgAGkNFhcwQh8Hw7mpBHbAIM1lV2bGPJEfhtx3oGBijtvbLyAKCNvSmsFr/NGKPW9oeUHFY2bF+xolqBuc4rv4JCNgKJLZMc7bV38H5gYzWdhN0iuu1S0EfAOtMFyTbMo+uBVV9g2kw2Go6k00OF1DTABnzMcxBH/vFX/xbon8U4dv7AqCZoWUD1ONqp2KA8I2Wj6gw5U+Oms5SfKOZFZSfbnUUnJk4aL/ABcayLn+iu+1rRTpkU38PRQrMzk+fyFZ01HTPiLoyrbrG8hyxA6n1rV3aNGNR3UZBB5gPI1TV/wzDExkC5I8sUvHkdUiufjY57lsp7iu0n0jSGJchrg92Menn+1A20H4nQROELyGPvB6532qbcf2j6jeaXw7bqTPc3A8I8skKP3P7Ue414Th4X0WOzTB5UEWcdCOpp0ZcEBH6f8AyZN9JIp3hmOa6iktsY7s5670Ym0qVSQcnNDOG5U0/iKW3ceCcmME+RzkVYEthGyZBFDkzVJN9G4PGUo8X2iHNpGPEwpstk82oRQKgzzADPzqVXEUMYIZhQXTgbjVC0fh8XKp9K88/sq/hxgkpdkv1bhtdX464etO7yuoGC3OwOcy4/oa9IrOyitbO2tYkwsMQjG3kKwZwjYw6r2kcIQJzMYr+2VSPIiQE/0Br0KjtUEaBTnAxVOLZyPqEGmkMVgYMuBvms9SQue2LiZmb8sozt/pTatMCDlYEr0NZuhLz9sPFnMhIFzkfTlFPatHNT/Rc+j24jgQAbADAo9HbllBK0y0a2LWsTsuCV3A8qPJbtyjlG1T9De0qBENsO/AJqQ29sQo8JzQ+2sw1yvzqTW9vhQoxXpKgYO1sZpbex9K6+FI9KKx24Gxwa+a0ck8oGf6VnQygJPbAKdhv61V/HnCdlfaLeaVPcGOOdNnX9BG6ke4OKuWaxLdRUF4z4YmjWXUzdl4CRmIj8n16YpGfHyVnS+n5Ywk4S9mRdf13iDRA1nq+mT3XdjlFxajvDIBtkr5Gq/1rj9Qp+C024d8HeZQgU++5zVw8ePG13NC645s4wdhVI65ZxtI6qMHON6mUot0zqfZe3Ec9mWgPq2tHi/VsSPaXCnxDAyq+FR6DJyfpTztV1Gxv1k7iVWCL0z1JqJ6jrmv6HoD2GkxgmWXm3B2Bxnp8hUT1HX9XngK3UIWUjdcHGa9OcpMbhTxKyM6jpMguxdI2JC3NketGba94uEYEdrHcoR+flO3z3oOmo3txOsUsYZs+mBU2sHEVukZYbKBtRN/GmAsMfufdRFrlNZun/xUfd+vLmiejWZhlR8+JSOtEbtVkcYOx61+2sUIkUnPUHHrQvrihs4yj8zQH4cOzrXdf4+0jiYWB/hmlyG5kuWHh5gCAo9Tk1t6K0KqAIs/Ssa9kvabf8HcMw2OnSIhJLEMAwyT5g1a2j/iNvu/WG8sYpQfzMPCAfbeunhpRPm/OyPJkr9F8i2YkZTH0rLuio83bBxg0gIxduE23xzH/YVc+jdsmganGslwWtGz1kIK/f8A7VVPD2m3cfaXxJq7gPa39yZraQHKshc',
			cabinetTotal: {
				total: '',
				fault: '',
				free: '',
				assign: ''
			},
			intervalTime: null,
			title: '智能随身物品柜',
			typeData: [
				{ title: '总量', value: 0, icon: require('@/assets/images/1.png') },
				{ title: '空闲', value: 0, icon: require('@/assets/images/2.png') },
				{ title: '占用', value: 0, icon: require('@/assets/images/3.png') },
				{ title: '故障', value: 0, icon: require('@/assets/images/4.png') }
			],
			configParams: {
				iotAddress: '',
				productKey: '',
				productName: '' // 默认值：附物管理终端(IP)
			},
			config: this.$store.getters['getTerminalConfig']
		}
	},
	created() {
		this.systemConfig = store.state.config.terminalConfig
		// this.getToken()
		
	},
	mounted() {
		localStorage.removeItem('facereturn')
		this.getCabinetBox()
		// const { iotAddress, productKey, productName } = this.params.isWeb ? this.params.configParams : this.config
		this.intervalTime = setInterval(() => {
			// this.getCabinetSide()
			// this.getCabinetBox()
		}, 30 * 1000)
	},
	beforeDestroy() {
		clearInterval(this.intervalTime)
		this.intervalTime = null
		this.$Modal.remove()
	},
	methods: {
		  // 获取线上配置
        getGlobalConfig(systemConfig) {
            this.$Get(this.AmtCom.getCabinetISetting, { ip: this.serverConfig.iotAddress  || this.serverConfig.ip })
                .then((res) => {
                    if (res.success ) {
                        const config = { ...systemConfig, ...res.data }
						console.log(config,'configconfigconfig获取线上配置')
                        this.updateConfig(config)
                    } else if (res.success && !res.data) {
                        // this.openConfigDialog()
                    } else {
                        // this.openErrorPrompt('服务配置错误，请联系管理员！')
						this.$GxxMessage({ message: '服务配置错误，请联系管理员！', type: 'err' })
                    }
                })
                .catch((err) => {
                    // console.log(err)
					// this.$GxxMessage({ message: '服务错误(404)，请联系管理员！', type: 'err' })
                    // this.openErrorPrompt('服务错误(404)，请联系管理员！')
                })
        },
		updateAppInfo(config, isReload = true) {
            this.writeConfigFile(config)
            this.setStoreConfig(config)
            if (isReload) {
                sessionStorage.clear()
                window.location.reload()
            }
        },
        setStoreConfig(config) {
            this.logger('网页设置的配置', config)
            this.$store.commit('setTerminalConfig', config)
        },
        writeConfigFile(config) {
            ipcRenderer && ipcRenderer.send('setTermConfig', config)
        },
		getToken() {
			const params = {
				grant_type: 'client_credentials',
				client_id: this.serverConfig?.clientName || '',
				client_secret: this.serverConfig?.clientPwd || ''
			}
			this.$http.bsp.getOauthToken(params).then((res) => {
				if (res) {
					setToken(res.access_token)
					this.getGlobalConfig(this.systemConfig)
					// this.getCabinetSide()
					this.getCabinetBox()
				}
			})
		},
		getCabinetSide() {
			const params = {
				centerId: this.serverConfig.centerId,
				cabinetCode: this.serverConfig.cabinetCode
			}
			this.$Post(this.PymApi.GET_CABINETSIDE, params).then((res) => {
				if (res.success) {
					localStorage.setItem('SIDES', JSON.stringify(res.sides))
				}
			})
		},
		getCabinetBox() {
			const params = {
				// cabinetCode: this.serverConfig.cabinetCode,
				// centerId: this.serverConfig.centerId,
				// searchObj: '{}',
				// deviceType: ''
				ip: this.serverConfig.iotAddress  || this.serverConfig.ip
			}
			console.log(this.serverConfig.iotAddress,'this.serverConfig.iotAddress',this.serverConfig)
			this.$Get(this.AmtCom.getCabinetGridStatusByIp, params).then((res) => {
				if (res.success) {
					this.dealData(res.data)
				}
			})
		},
		dealData(res) {
			this.typeData[0].value = res.total
			this.typeData[3].value = res.failure // 故障
			this.typeData[2].value = res.occupied // 占用
			this.typeData[1].value = res.free // 空闲
		},
		putInAction() {
			this.$router.push({
				path: 'faceLogin',
				query: {
					fromPath: '/home',
					toPath: '/depositItems' // '/scanCode'
				}
			})
		},
		takeOutAction() {
			this.$router.push({
				path: 'faceLogin',
				query: {
					fromPath: '/home',
					toPath: '/selfbatchTakeOut'
				}
			})
		}
	}
}
</script>

<style lang="less" scoped>
@assets: '../../../assets/images';

.offline-warp {
	width: 600px;
	height: 400px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;

	.tipsIcon {
		display: inline-block;
		width: 100px;
		height: 100px;
		background-image: url(data:image/png;base64,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);
		background-size: 100% 100%;
		margin-bottom: 30px;
	}

	.tips {
		font-size: 32px;
		text-align: center;
		margin-bottom: 60px;
		color: #2863ca;
	}

	.tool {
		display: flex;
		justify-content: center;

		span.btn {
			display: inline-flex;
			width: 150px;
			height: 50px;
			font-size: 24px;
			border-radius: 4px;
			text-align: center;
			line-height: 50px;
			text-align: center;
			justify-content: center;
			background: #0099e6;
			color: #fff;
			cursor: pointer;
		}
	}
}

#home {
	width: 100vw;
	// height: 100vh;
	display: flex;
	justify-content: space-between;
	flex-direction: column;
	background: url('@{assets}/bg.png') no-repeat;
	background-size: 100% 100%;
	overflow-x: hidden;
	position: relative;
	padding-bottom: 20px;
	.title-app {
		font-family: Source Han Sans CN, Source Han Sans CN;
		font-weight: bold;
		font-size: 62px;
		color: #042767;
		line-height: 80px;
		margin: 195px 0 165px 100px;
	}

	.TopBox {
		// height: 1200px;
		display: flex;
		align-items: center;
		margin: 50px 0 100px 0;
	}

	.selectionType {
		width: 100%;
		margin: 0 100px;

		h3 {
			width: 100%;
			font-size: 48px;
			font-weight: 700;
			text-align: center;
			color: #2e71e6;
			line-height: 36px;
			letter-spacing: 5px;
		}

		.vertical {
			div {
				font-size: 90px;
				color: #fff;
				line-height: 376px;
				padding-left: 70px;

				&:first-child {
					width: 100%;
					height: 376px;
					background: url('@{assets}/cr_icon.png') no-repeat;
					background-size: 100% 100%;
				}

				&:nth-child(2) {
					margin-top: 80px;
					width: 100%;
					height: 376px;
					background: url('@{assets}/qc_icon.png') no-repeat;
					background-size: 100% 100%;
				}
			}
		}
	}

	.count {
		width: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin-top: 40px;

		.type-wrap {
			width: 100vw;
			display: flex;
			flex-wrap: wrap;
			padding: 0 0 0 100px;

			li {
				width: 43%;
				background: linear-gradient(181deg, #e6f3ff 0%, #ffffff 100%);
				box-shadow: 0px 8px 16px 1px rgba(115, 157, 229, 0.3);
				border-radius: 8px 8px 8px 8px;
				height: 119px;
				margin: 0 40px 30px 0;
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 35px;

				.type-wrap-title {
					font-family: MicrosoftYaHei, MicrosoftYaHei;
					font-weight: normal;
					font-size: 36px;
					color: #64759a;
				}

				.type-wrap-value {
					font-family: Arial-BoldMT, Arial-BoldMT;
					font-weight: normal;
					font-size: 48px;
					color: #2b4d71;
				}
			}
		}
	}
}

.open-dev-tool {
	background-color: #d9ebff;
	text-align: center;
	width: 80px;
	height: 32px;
	line-height: 32px;
	margin: 0px auto;
	font-size: 20px;
	float: right;
	color: #0099e6;
	font-weight: 600;
	cursor: pointer;
	border-radius: 4px;
}

.type-flex {
	display: flex;
	align-items: center;
}

.copyright {
	width: 100vw;
	font-family: Source Han Sans CN, Source Han Sans CN;
	font-weight: 400;
	font-size: 20px;
	color: #2b5fda;
	text-align: center;
	margin-bottom: 25px;
	position: absolute;
	bottom: 0px;
}
</style>
