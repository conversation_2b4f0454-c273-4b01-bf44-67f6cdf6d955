<template>
	<div id="depositItems">
		<headNav :show-box-index="[false, false, false]" :sysTitle="sysTitle"></headNav>
		<div style="width: 100%;">
			<div class="depositItems-main" v-if="lockControlProtocolList && lockControlProtocolList.length>0">
				<div v-for="(item, index) in lockControlProtocolList" :key="index" class="depositItems-main-children">
					<CheckboxGroup v-model="choosedCabinet" @on-change="checkAllGroupChange">
						<div class="flex-box">
							<p>
								<Checkbox :label="item.storageLocation"></Checkbox>
								<b class="BoxName">{{ item.storageLocationName }}</b>
							</p>
							<span class="jgryxm">被监管人员: {{ item.jgryxm }}</span>
						</div>
						<div v-for="(ele, i) in item.resultList" :key="i + 'box'" class="thingData">
							<img :src="ele.photoPathList && ele.photoPathList.length>0?ele.photoPathList[0]:ele.img" class="wpImg"  />
							<div>
								<p class="wpName">{{ ele.nameName }}</p>
								<p class="xz">特征:{{ ele.features ? ele.features : '-' }}</p>
							</div>
							<p class="num">{{ ele.quantity }}{{ ele.unitName }}</p>
						</div>
					</CheckboxGroup>
				</div>
			</div>
			<div v-else class="depositItems-main kongBox"></div>
		</div>
		<div>
			<div class="x-dialog-footer" style="width: 100%">
				<Button type="default" @click="cancel" style="width: 48%">取消</Button>
				<Button @click="handleNext" class="next-step" style="width: 48%">下一步</Button>
			</div>
		</div>
	</div>
</template>
<script>
import headNav from '_c/headNav'

export default {
	components: {
		headNav
	},
	data() {
		return {
			sysTitle: '请选择存入物品',
			configParams: {},
			lockControlProtocolList: [
				{ xm: '张三', boxName: 'A监区主柜B07号柜', thingData: [{ wpName: '水果刀', xz: '金属色、张小泉牌、有磕碰痕迹', num: '1把', img: '' }] },
				{ xm: '张三', boxName: 'A监区主柜B08号柜', thingData: [{ wpName: '书籍', xz: '灰色封皮、教育类、红楼梦-内容超长的…', num: '1台', img: '' }] }
			],
			choosedCabinet: [],
			choosedCabinetData:{num:''},
			wpList: []
		}
	},
	created() {
		this.getItemsTdoStoredListByJgrybm()
	},
	beforeDestroy() {},
	methods: {
		handleNext() {
			// this.$router.push('/choose_cabinet')
			console.log(this.choosedCabinet, 'checkAllGroupcheckAllGroupcheckAllGroup')
			if (this.choosedCabinet && this.choosedCabinet.length == 0) {
				return this.$GxxMessage({ message: '请选择存放位置！', type: 'warning' })
			}
			this.choosedCabinetData={num:''}
			this.choosedCabinet.forEach((ele,i)=>{
				this.choosedCabinetData.num+=ele+(Number(i+1)==this.choosedCabinet.length?'':',')
				
			})
			// this.choosedCabinet = this.choosedCabinetArr
			this.$store.commit('setCabinet', { cabinet: this.choosedCabinetData })
			this.$router.push({ name: 'cabinet_put_in', params: { getWplist: this.wpList } })
		},
		// 选中柜子回调
		handleChoosed(cabinet) {
			this.choosedCabinet = cabinet
		},
		checkAllGroupChange(data) {
			console.log(data, '12122checkAllGroupChange')
			let arr=[]
			if (data && data.length > 0) {
				data.forEach((ele) => {
					this.lockControlProtocolList.forEach((item) => {
						if (ele == item.storageLocation) {
							arr=this.wpList.concat(item.resultList)
							console.log(arr,'1212')
						}
					})
				})
			}
			this.wpList=arr
			console.log(this.wpList,'this.wpListthis.wpListthis.wpList')
		},
		cancel() {
			this.$router.push('/home')
		},
		getItemsTdoStoredListByJgrybm() {
			const params = {
				ip: this.serverConfig.iotAddress || this.serverConfig.ip,
				jgrybm: this.serverConfig.jgrybm
			}
			this.$Get(this.AmtCom.getItemsTdoStoredListByJgrybm, params).then((res) => {
				if (res.success) {
					this.lockControlProtocolList = res.data
				}
			})
		}
	}
}
</script>
<style scoped lang="less">
.wpImg {
	width: 216px;
	height: 140px;
	border-radius: 8px 8px 8px 8px;
	margin-right: 30px;
}
.depositItems-main {
	margin: 60px 40px;
	.depositItems-main-children {
		padding: 30px;
		// height: 448px;
		background: #ffffff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		border-radius: 8px 8px 8px 8px;
		margin-bottom: 32px;
		border: 1px solid transparent;
		cursor: pointer;
		&:hover {
			border: 1px solid #20dbe9;
		}
		.BoxName {
			font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
			font-weight: normal;
			font-size: 36px;
			color: #2b3346;
			line-height: 36px;
		}
		.jgryxm {
			font-family: Microsoft YaHei, Microsoft YaHei;
			font-weight: 400;
			font-size: 30px;
			color: #5f709a;
			line-height: 36px;
		}
		.thingData {
			margin-top: 30px;
			display: flex;
			position: relative;

			.wpName {
				font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
				font-weight: bold;
				font-size: 36px;
				color: #2b5fda;
				line-height: 36px;
				margin-bottom: 30px;
			}
			.xz {
				font-family: Microsoft YaHei, Microsoft YaHei;
				font-weight: 400;
				font-size: 30px;
				color: #2b3346;
				line-height: 36px;
			}
			.num {
				font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
				font-weight: bold;
				font-size: 36px;
				color: #2b5fda;
				line-height: 36px;
				position: absolute;
				right: 0;
			}
		}
	}
}
.flex-box {
	display: flex;
	justify-content: space-between;
}
.next-step {
	color: #ffffff;
	background: #2b5fda;
}
</style>