<template>
    <div id="cabinetOpen">
        <cabinet-head :cabinet-num="activeGh" :put-in="false" :status-code="statusCode"></cabinet-head>
        <cabinet-main :opened-cabinet-num="openedCabinetNum" :cabinet-detail="cabinetDetail" :current-cabinet-num="activeGh"></cabinet-main>
        <footer class="footer">
            <cabinet-footer :current-cabinet-num="activeGh" :put-in="false" :time="time" :status-code="statusCode" :show-more-btn="false" :show-back-home="showBackHome" @clickBackHome="$router.push('/home')" @clickCancel="$router.push('/home')" @clickManual="clickManual" @clickContinue="clickContinue"></cabinet-footer>
        </footer>
    </div>
</template>

<script>
import Lock from '@/libs/lock/index'
import { modalConfirm, modalInfo } from '@/libs/modal'
import { getGHEvent } from '@/libs/lock/util'
import { getUserInfo, voiceBroadcast } from '@/libs/utils.js'
import cabinetFooter from '../components/cabinetFooter.vue'
import cabinetHead from '../components/cabinetHead.vue'
import cabinetMain from '../components/cabinetMain.vue'
const userInfo = getUserInfo()
export default {
    components: {
        cabinetMain,
        cabinetHead,
        cabinetFooter
    },
    data() {
        return {
            activeGh: '',
            timer1: '',
            retryTimes: 3,
            flag: 3,
            wpList: [], // 全部取出对象列表
            faultWpObjArr: [], // 不能开柜的物品对象列表
            awaitCabinetDoor: '', // 剩下待开启的门
            activeWpObj: {}, // 当前活跃的物品对象
            statusCode: 1,
            openedCabinetNum: [],
            cabinetDetail: [],
            time: +this.serverConfig.loginOutTime,
            lockObj: null,
            showBackHome: false
        }
    },
    watch: {
        activeGh: {
            async handler(newVal) {
                if (this.openedCabinetNum.findIndex((item) => item == newVal) == -1) {
                    this.openedCabinetNum.push(newVal)
                }
                this.cabinetDetail = await this.renderCabinet()
            }
        }
    },

    created() {
        // 自助批量取出物品列表
        this.wpList = JSON.parse(localStorage.getItem('selfbatchTakeOut'))
        console.log(this.wpList,'this.wpListthis.wpListthis.wpListthis.wpList')
        // 初始化柜号并初始化当前操作的物品对象
        this.activeGh = this.wpList[0].gh
        this.activeWpObj = this.wpList[0]
        const awaitCabinetDoorArr = []
        this.wpList.forEach((item) => {
            awaitCabinetDoorArr.push(item.gh)
        })
        this.awaitCabinetDoor = awaitCabinetDoorArr.join(',').substring(4)
        this.initLock(() => {
            this.openLock(this.activeGh)
        })
    },
    beforeDestroy() {
        clearInterval(this.timer1)
        this.destroyedLock()
        voiceBroadcast(false)
        this.$Modal.remove()
    },
    methods: {
        initLock(callback) {
            if (this.lockObj) {
                callback && callback()
                return
            }
            this.lockObj = new Lock()
            this.lockObj.on('open', (data) => {
                this.logger({ title: 'open-存入查锁记录-串口', value: data })
                if (data.code == 0) {
                    callback && callback()
                } else {
                    this.destroyedLock()
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '连接锁控服务失败！4!', (flag) => {
                        if (!flag) {
                            this.$router.push('/home')
                            return
                        }
                        setTimeout(() => {
                            this.initLock(callback)
                        }, 1000)
                    })
                }
            })
            this.lockObj.on('error', (data) => {
                this.logger({ title: 'error-存入查锁记录-串口', value: data })
                this.destroyedLock()
                modalConfirm('重新连接', '返回首页', 'tipsIcon2', data.code == 1 ? '连接锁控服务失败5！' : data.msg, (flag) => {
                    if (!flag) {
                        this.$router.push('/home')
                        return
                    }
                    setTimeout(() => {
                        this.initLock(callback)
                    }, 1000)
                })
            })
            this.lockObj.on('message', (data) => {
                this.logger({ title: 'message-存入查锁记录-串口', value: data })
                const { result, actionType, box, door } = data
                const gh = getGHEvent(box, door)
                if (!actionType) {
                    this.destroyedLock()
                    modalConfirm('重新连接', '返回首页', 'tipsIcon2', '连接锁控服务失败了！', (flag) => {
                        if (!flag) {
                            this.$router.push('/home')
                            return
                        }
                        setTimeout(() => {
                            this.initLock(this.openLock(gh))
                        }, 1000)
                        return
                    })
                }
                if (actionType == 'openlock') {
                    // result：1：门开启、0：门关闭
                    if (result == 1) {
                        this.statusCode = 2
                        this.faultWpObjArr = this.faultWpObjArr.filter((item) => {
                            return item.id !== this.activeWpObj.id
                        })
                        setTimeout(() => {
                            this.checkLock(gh)
                        }, 2000)
                    }
                    if (result == -1) {
                        modalInfo('返回首页', 'tipsIcon2', data.msg, (val) => {
                            if (val) this.$router.push('/home')
                        })
                    }
                    if (result == 0) {
                        if (this.retryTimes == 0) {
                            // 三次都失败，打开下一个柜格,并且发送故障请求
                            // 准备打开下一个柜子
                            // 若不在故障列表中，则加入故障列表中
                            if (JSON.stringify(this.faultWpObjArr).indexOf(this.activeWpObj.gh) == -1) {
                                this.sendFaultCabinet(this.activeWpObj.gh) // 发送故障请求
                            }
                            if (JSON.stringify(this.faultWpObjArr).indexOf(this.activeWpObj.id) == -1) {
                                this.faultWpObjArr.push(this.activeWpObj) // 当前检测出存在故障的柜子
                            }

                            this.wpList.forEach((item, index) => {
                                if (item.id == this.activeWpObj.id) {
                                    this.wpList.splice(index, 1)
                                }
                            })
                            // 三次都失败了打开弹出框
                            setTimeout(() => {
                                this.retryTimes = 3
                                this.openErrorModal()
                            }, 1500)
                            return
                        }
                        this.retryTimes--
                        this.openLock(gh)
                    }
                }
                if (actionType == 'checklock') {
                    // result：1：门开启、0：门关闭
                    if (result == 1) {
                        console.log('checkLock', result)
                        // 开门后检查柜门状态
                        setTimeout(() => {
                            this.checkLock(gh)
                        }, 2000)
                    }
                    if (result == 0) {
                        // 检测到门关闭之后将状态字设置为 3:柜门已关闭
                        this.takeOutArchiveOperate()
                    }
                }
            })
            this.lockObj.init()
        },
        destroyedLock() {
            this.lockObj && this.lockObj.destroyed()
            this.lockObj = null
        },
        openLock(gh) {
            this.lockObj && this.lockObj.handle('openlock', gh)
        },
        checkLock(gh) {
            this.lockObj && this.lockObj.handle('checklock', gh)
        },
        openErrorModal() {
            const tips2 = '开锁失败，请联系管理员'
            voiceBroadcast(true, tips2, 'openLockFailed')
            // 判断是否存在下一柜门
            this.activeGh = this.awaitCabinetDoor.substring(0, 3)
            if (this.activeGh) {
                modalConfirm('继续开柜', '返回首页', 'tipsIcon2', '开锁失败，请联系管理员', (val) => {
                    if (val) {
                        this.activeWpObj = this.wpList[0]
                        this.initLock(() => {
                            this.openLock(this.activeGh)
                        })
                        this.awaitCabinetDoor = this.awaitCabinetDoor.substring(4)
                        return
                    }
                    this.$router.push('/home')
                })
            } else {
                modalInfo('返回首页', 'tipsIcon2', '开锁失败，请联系管理员', (val) => {
                    if (val) this.$router.push('/home')
                })
            }
        },
        openNextDoor() {
            const _this = this
            if (_this.awaitCabinetDoor.substring(0, 3)) {
                _this.activeGh = _this.awaitCabinetDoor.substring(0, 3)
                _this.activeWpObj = _this.wpList[0]
                _this.retryTimes = 3
                _this.openLock(_this.activeGh)
                _this.awaitCabinetDoor = _this.awaitCabinetDoor.substring(4)
            } else {
                this.showBackHome = true
            }
        },
        // 取出物品操作
        takeOutArchiveOperate() {
            const that = this
            const params = {
                // wpid: this.activeWpObj.id,
                // cabinetCode: this.serverConfig.cabinetCode,
                // centerId: this.serverConfig.centerId,
                // xm: userInfo.name,
                // sfzh: userInfo.idCard,
                // gh: this.activeWpObj.gh
                    ip: this.serverConfig.iotAddress  || this.serverConfig.ip,
                    jgrybm:  this.serverConfig.jgrybm,
                    storageLocation: this.activeWpObj.gh,
                    wpbhList:this.wpList && this.wpList.length>0?JSON.stringify(this.wpList):''  //[]
            }
            // 保存取出信息
            this.$Post_Json(this.AmtCom.taskOutItemsUpdateStatus, params).then((res) => {
                // 保存取出信息成功
                if (res.success && res.data) {
                    // 3:柜门已关闭
                    this.statusCode = 3
                    const tips = `${this.activeWpObj.gh}号柜子取出物品成功`
                    voiceBroadcast(true, tips, 'takeOutSuccess')
                    that.wpList.forEach((item, index) => {
                        if (item.id == that.activeWpObj.id) {
                            that.wpList.splice(index, 1)
                        }
                    })
                    setTimeout(() => {
                        that.openNextDoor()
                    }, 2000)
                } else {
                    voiceBroadcast(true, '更新数据失败，请联系管理员', 'updateErr')
                    that.ModalConfirm('重试', '返回首页', 'tipsIcon2', '更新数据失败，请联系管理员', (val) => {
                        if (val) {
                            setTimeout(() => {
                                that.takeOutArchiveOperate()
                            }, 1500)
                        } else {
                            that.$router.replace('/home')
                        }
                    })
                }
            })
        },
        sendFaultCabinet(item) {
            // 发送故障请求
            const params = {
                cabinetCode: this.serverConfig.cabinetCode,
                centerId: this.serverConfig.centerId,
                gh: item,
                gzyy: '不能开柜'
            }
            this.$Post(this.PymApi.saveCabinetFault, params)
        },
        renderCabinet() {
            return new Promise((resolve, reject) => {
                const cabinetArr = JSON.parse(localStorage.getItem('SIDES'))
                const curRendCabinet = ['A']
                if (cabinetArr.length && cabinetArr.length > 1) {
                    const sideCabinetId = this.activeGh.substring(0, 1) == 'A' ? cabinetArr.find((item) => item.sideCode != 'A').sideCode : this.activeGh.substring(0, 1)
                    curRendCabinet.push(sideCabinetId)
                }
                const result = []
                curRendCabinet.forEach(async(item) => {
                    const sideId = cabinetArr.find((item2) => item2.sideCode === item).id
                    const params = {
                        sideId: sideId
                    }
                    const res = await this.$Post(this.PymApi.GET_SIDEDETAIL, params)

                    result.push({
                        ...res,
                        sideCode: item,
                        orderId: item === 'A' ? 1 : 2
                    })
                    result.sort((a, b) => {
                        return a.orderId - b.orderId
                    })
                })
                resolve(result)
            })
        },
        clickContinue() {
            if (this.$route.path == '/cabinetOpenPutIn') {
                this.$router.replace('/scanCode')
            } else if (this.$route.path == '/cabinetsOpenTakeOut') {
                this.$router.replace('/selfbatchTakeOut')
            }
        },
        clickManual() {
            this.currentCase = JSON.parse(JSON.stringify(this.currentCaseCopy))
            console.log(this.currentCase)

            this.re_openLock(this.activeGh)
        }
    }
}
</script>

<style lang="less" scoped>
.footer {
	height: 250px;
	width: 940px;
	margin: 110px auto 0 auto;
}
.tool {
		display: flex;
		justify-content: center;
		margin-top: 60px;
		.fault-btn {
			width: 264px;
			height: 64px;
			background: #f26b6b;
			border-radius: 32px;
			font-size: 24px;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
		}
		&.tool-closed {
			span {
				width: 320px;
				height: 96px;
				border-radius: 8px;
				font-size: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 60px;
				cursor: pointer;
			}
			.replay-btn {
				border: 2px solid #f84558;
				color: #f84558;
				background-color: #fff;
			}
			.back-btn {
				background-color: #337eff;
				color: #fff;
			}
		}
	}
</style>
