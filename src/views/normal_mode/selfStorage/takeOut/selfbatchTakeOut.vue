<template>
	<div id="temporaryStorageMain">
		<headNav :login-status="loginStatus" :show-box-index="[true, true, true, true]"></headNav>
		<h2>待取出的物品</h2>
		<div class="caseListBox infinite-list-wrapper" style="position: relative">
			<div v-if="kong" class="kongBox"></div>
			<div v-if="!kong" v-gxx-infinite-scroll="load" gxx-infinite-scroll-disabled="disabled" :gxx-infinite-scroll-distance="20">
				<!-- <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
                    <template v-for="(item, index) in newCaseList">
                        <Checkbox :key="index" :label="item.storageLocation" :class="selectIndex == index ? 'caseItem active' : 'caseItem'" @click="radioClick(item, index)">
                            <div class="caseItemCont">
                                <div class="caseItemTop">
                                    <label class="serialNumber">{{ index + 1 > 9 ? index + 1 : (index + 1).toString().padStart(2, 0) }}</label>
                                    <div class="content">
                                        <p class="name">{{ item.wpbh ? item.wpbh : '物品编号' }}</p>
                                        <p class="dwmc">{{ item.wpmc ? item.wpmc : '案件名称' }} {{ item.wpsl }} ({{ item.jldw }})</p>
                                    </div>
                                    <div class="count">{{ item.gh }}</div>
                                </div>
                            </div>
                        </Checkbox>
                    </template>
                </CheckboxGroup> -->
				<div class="depositItems-main">
					<div v-for="(item, index) in newCaseList" :key="index" class="depositItems-main-children">
						<!-- <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange"> -->
							<div class="flex-box">
								<p>
									<!-- <Checkbox :label="item.storageLocation"></Checkbox> -->
									<b class="BoxName">{{ item.storageLocationName }}</b>
								</p>
								<span class="jgryxm">被监管人员: {{ item.jgryxm }}</span>
							</div>
							<div v-for="(ele, i) in item.resultList" :key="i + 'box'" class="thingData">
								<img :src="ele.photoPathList && ele.photoPathList.length>0?ele.photoPathList[0]:ele.img" class="wpImg"  />
								<div>
									<p class="wpName">{{ ele.nameName }}</p>
									<p class="xz">特征:{{ ele.features ? ele.features : '-' }}</p>
								</div>
								<p class="num">{{ ele.quantity }}{{ ele.unitName }}</p>
							</div>
						<!-- </CheckboxGroup> -->
					</div>
				</div>
			</div>
			<p v-if="loading && !noMore" class="loadTips">加载中...</p>
			<p v-if="noMore && newCaseList.length > 0" class="loadTips">没有更多了</p>
		</div>
		<div class="comfirmBox">
			<!-- <Checkbox :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox> -->
			<div class="right">
				<span>
					共计
					<i>{{ total }}</i>
					件，
				</span>
				<!-- <span>
					已选
					<i>{{ checkAllGroup.length }}</i>
					件
				</span> -->
				<div class="buttonStyle2" @click="confirmAction">立即取出</div>
			</div>
		</div>
	</div>
</template>

<script>
import headNav from '_c/headNav'
export default {
	components: {
		headNav
	},
	data() {
		return {
			loading: false,
			total: 0,
			loginStatus: '',
			searchText: '',
			selectIndex: -1,
			checkAll: false,
			checkAllGroup: [],
			caseList: [],
			newCaseList: [],
			kong: false,
			sIndex: 0
		}
	},
	computed: {
		noMore() {
			return this.newCaseList.length >= this.total
		},
		disabled() {
			return this.loading || this.noMore
		}
	},
	created() {
		this.getCaseList()
	},
	beforeDestroy() {
		this.$Modal.remove()
	},
	methods: {
		load() {
			const newCaseList = JSON.parse(JSON.stringify(this.caseList))
			this.loading = true
			const sIndex = this.sIndex
			const newArr = newCaseList.splice(sIndex, 6)
			this.newCaseList.push(...newArr)
			this.sIndex = this.sIndex + 6
			setTimeout(() => {
				this.loading = false
			}, 2000)
		},
		getCaseList() {
			const params = {
				// cabinetCode: this.serverConfig.cabinetCode,
				// centerId: this.serverConfig.centerId
				ip: this.serverConfig.iotAddress || this.serverConfig.ip,
				jgrybm: this.serverConfig.jgrybm   // '6261927217740773'
			}
			this.$Get(this.AmtCom.getTaskOutListByJgrybm, params).then((res) => {
				//this.PymApi.getSelectWpxxList
				if (res.success) {
					if (res.data.length > 0) {
						this.caseList = [...res.data]
						this.total = this.caseList.length || 0
						this.load()
					} else {
						this.kong = true
					}
				} else {
					this.ModalInfo('确认', 'tipsIcon2', res.msg)
				}
			})
		},
		radioClick(item) {
			const index = this.checkAllGroup.indexOf(item.id)
			if (index > -1) {
				this.checkAllGroup.splice(index, 1)
			} else {
				this.checkAllGroup.push(item.id)
			}
		},
		checkAllGroupChange(arr) {
			this.checkAllGroup = arr
			this.checkAll = arr.length == this.caseList.length
		},
		handleCheckAll() {
			this.checkAllGroup = []
			this.checkAll = !this.checkAll
			if (this.checkAll) {
				this.checkAllGroup = this.caseList.map((item) => item.storageLocation)
			}
		},
		confirmAction() {
			const ghList = []
			const caseList = []
			// ???
			// this.caseList.forEach((item) => {
			// 	this.checkAllGroup.forEach((item2) => {
			// 		if (item.storageLocation == item2) {
			// 			caseList.push(item)
			// 			if (!ghList.includes(item.gh)) {
			// 				ghList.push(item.gh)
			// 			}
			// 		}
			// 	})
			// })
            this.caseList.forEach((item) => {
                if(item.resultList && item.resultList.length>0){
                    item.resultList.forEach(ele=>{
						this.$set(ele,'storageLocation',item.storageLocation)
                        ele.gh=item.storageLocation//storageLocationName
                        caseList.push(ele)
                    })
                }
                ghList.push(item.storageLocation)
            })
			if (ghList.length > 0) {
				localStorage.setItem('selfbatchTakeOut', JSON.stringify(caseList))
				this.$router.replace({
					path: '/cabinetsOpenTakeOut',
					query: {
						CabinetDoor: ghList.join(','),
						CabinetNum: ghList.length
					}
				})
			} else {
				this.ModalInfo('确认', 'tipsIcon2', '请选择物品！')
			}
		}
	}
}
</script>

<style>
.caseItem .ivu-checkbox,
.caseItemCont {
	float: left;
}
.caseItem .ivu-checkbox {
	width: 8%;
	margin-top: 40px;
	margin-left: 2%;
	text-align: center;
}
.caseItemCont {
	width: 90%;
	padding: 25px 30px 0 10px;
}
</style>
<style lang="less" scoped>
@assets: '../../../../assets/images';
#temporaryStorageMain {
	h2 {
		width: 100%;
		font-size: 48px;
		font-weight: 700;
		text-align: center;
		color: #2e71e5;
		line-height: 36px;
		letter-spacing: 5px;
		margin: 38px 0;
	}
	.searchBox {
		// width: 984px;
		position: relative;
		margin: 64px auto;
		input {
			width: 100%;
			height: 96px;
			background: #ffffff;
			border: 2px solid #337eff;
			border-radius: 18px;
			color: #3663b3;
			font-size: 40px;
			padding-left: 32px;
			box-sizing: border-box;
		}
		input:focus-visible {
			outline: none;
			background: #fff;
		}
		.clearBtn {
			display: inline-block;
			width: 64px;
			height: 64px;
			background: url('@{assets}/clearBtn.png') no-repeat;
			background-size: 100% 100%;
			position: absolute;
			right: 32px;
			top: 16px;
		}
	}
	.caseListBox {
		// width: 984px;
		height: 1140px;
		overflow: auto;
		margin: 0 auto;
		.caseItem {
			padding: 15px 0;
			width: 99%;
			background: #ffffff;
			border-radius: 16px;
			box-shadow: 0px 4px 6px 0px #cedcf5;
			margin-bottom: 24px;

			box-sizing: border-box;
			.caseItemTop {
				display: flex;
				align-items: center;

				position: relative;
				padding-bottom: 10px;
				.serialNumber {
					display: inline-block;
					width: 80px;
					text-align: center;
					font-size: 32px;
					color: #333333;
					line-height: 30px;
				}
				.content {
					width: 72%;
					margin-left: 39px;
					.name {
						font-size: 36px;
						color: #3663b3;
						line-height: 32px;
						margin-bottom: 20px;
					}
					.dwmc {
						font-size: 24px;
						color: #333333;
						line-height: 32px;
					}
					.null {
						font-size: 24px;
						color: #999;
						line-height: 32px;
					}
				}
				.count {
					font-size: 40px;
					font-weight: 700;
					text-align: right;
					color: #4f5eff;
					line-height: 30px;
					position: absolute;
					right: 0;
				}
			}
			.caseItemBottom {
				padding: 10px 0 10px 0px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				span {
					font-size: 24px;
					line-height: 32px;
					color: #999;
					display: inline-block;
					i {
						font-size: 24px;
						color: #333;
					}
				}
			}
			&.active {
				background: #d6e5ff;
			}
		}
	}
	.comfirmBox {
		// width: 984px;
		height: 160px;
		background: #fff;
		border-radius: 16px;
		margin: 20px auto 0;
		// position: absolute;
		// bottom: 46px;
		// left: calc(~'50% - 492px');
		display: flex;
		justify-content: flex-end;
		align-items: center;
		padding: 0 34px;
		.right {
			display: flex;
			align-items: center;
            justify-content: end;
			span {
				font-size: 26px;
				color: #666666;
				i {
					color: #337eff;
					font-size: 26px;
				}
			}
			.buttonStyle2 {
				margin: 0 0 0 32px;
			}
		}
	}
}
</style>
<style scoped lang="less">
.wpImg {
	width: 216px;
	height: 140px;
	border-radius: 8px 8px 8px 8px;
	margin-right: 30px;
}
.depositItems-main {
	margin: 60px 40px;
	.depositItems-main-children {
		padding: 30px;
		// height: 448px;
		background: #ffffff;
		box-shadow: 0px 10px 10px 1px rgba(115, 157, 229, 0.15), inset 0px 3px 3px 1px rgba(48, 116, 218, 0.1);
		border-radius: 8px 8px 8px 8px;
		margin-bottom: 32px;
		border: 1px solid transparent;
		cursor: pointer;
		&:hover {
			border: 1px solid #20dbe9;
		}
		.BoxName {
			font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
			font-weight: normal;
			font-size: 36px;
			color: #2b3346;
			line-height: 36px;
		}
		.jgryxm {
			font-family: Microsoft YaHei, Microsoft YaHei;
			font-weight: 400;
			font-size: 30px;
			color: #5f709a;
			line-height: 36px;
		}
		.thingData {
			margin-top: 30px;
			display: flex;
			position: relative;

			.wpName {
				font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
				font-weight: bold;
				font-size: 36px;
				color: #2b5fda;
				line-height: 36px;
				margin-bottom: 30px;
			}
			.xz {
				font-family: Microsoft YaHei, Microsoft YaHei;
				font-weight: 400;
				font-size: 30px;
				color: #2b3346;
				line-height: 36px;
			}
			.num {
				font-family: MicrosoftYaHei-Bold, MicrosoftYaHei-Bold;
				font-weight: bold;
				font-size: 36px;
				color: #2b5fda;
				line-height: 36px;
				position: absolute;
				right: 0;
			}
		}
	}
}
.flex-box {
	display: flex;
	justify-content: space-between;
}
.next-step {
	color: #ffffff;
	background: #2b5fda;
}
</style>
