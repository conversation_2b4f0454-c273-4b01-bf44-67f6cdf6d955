// 当前util工具不规范，以后需优化一下，暂存模式得开锁写在lock.js
import { filterGh } from './filterGh'
import Vue from 'vue'
import router from '@/router'
import store from '@/stroe'
import { modalInfo, modalConfirm } from './modal'
import { GxxDialogConfirm } from 'gosuncn-ui'
import dealDialog from '@/components/dealDialog'
const serverConfig = JSON.parse(localStorage.getItem('serverConfig'))

// 设置cookie对应key区域------------------------------开始
export const TOKEN_KEY = 'token'
export const NEW_TOKEN_KEY = 'newToken'
// --------------------------------------------------结束
export const setToken = (token) => {
    sessionStorage.setItem(TOKEN_KEY, token)
}
export const getToken = () => {
    const token = sessionStorage.getItem(TOKEN_KEY)
    if (token) return token
    else return false
}
export const setNewToken = (token) => {
    sessionStorage.setItem(NEW_TOKEN_KEY, token)
}
export const getNewToken = () => {
    const token = sessionStorage.getItem(NEW_TOKEN_KEY) || sessionStorage.getItem(TOKEN_KEY)
    if (token) return token
    else return false
}
export const removeNewToken = () => {
    sessionStorage.removeItem(NEW_TOKEN_KEY)
}
export const clearCookie = () => {
    var keys = document.cookie.match(/[^ =;]+(?=\=)/g)
    if (keys) {
        for (var i = keys.length; i--;) {
            document.cookie = keys[i] + '=0;expires=' + new Date(0).toUTCString()
        }
        location.reload()
    }
}
export const clear = () => {
    sessionStorage.removeItem(TOKEN_KEY)
}

String.prototype.replaceAll = function(s1, s2) {
    return this.replace(new RegExp(s1, 'gm'), s2)
}

export const getUserInfo = () => {
    var userInfo = JSON.parse(localStorage.getItem('userInfo'))
    return userInfo
}
/**
 * @param {String} url
 * @description 从URL中解析参数
 */
export const getParams = (url) => {
    if (url.indexOf('?') < 0) return {}
    const keyValueArr = url.split('?')[1].split('&')
    const paramObj = {}
    keyValueArr.forEach((item) => {
        const keyValue = item.split('=')
        paramObj[keyValue[0]] = keyValue[1]
    })
    return paramObj
}

// websocket 通用连接方法
var websocket
export const initWebSocket = (url, callback) => {
    if ('WebSocket' in window) {
        if (url) {
            websocket = new WebSocket(url)
            // 连接成功建立的回调方法
            websocket.onopen = function(e) {
                console.log('WebSocket——lock连接成功')
                console.log(e)
                if (callback) {
                    callback(websocket)
                }
            }
            // 连接发生错误的回调方法
            websocket.onerror = function(e) {
                if (url.includes('light')) {
                    console.log('WebSocket——light连接发生错误')

                    modalConfirm('重新加载', '确定', 'tipsIcon2', '补光灯通信服务连接失败', (val) => {
                        if (val) {
                            router.replace('/home')
                            location.reload()
                        }
                    })
                } else if (url.includes('locker')) {
                    console.log('WebSocket——locker连接发生错误')
                    modalConfirm('返回首页', '确定', 'tipsIcon2', '锁控通信服务连接失败', (val) => {
                        if (val) {
                            console.log('返回首页', val)
                            router.replace('/home')
                            location.reload()
                        } else {
                            console.log('确定')
                        }
                    })
                } else {
                    modalConfirm('返回首页', '确定', 'tipsIcon2', '柜子通信服务连接失败', (val) => {
                        if (val) {
                            // location.reload()
                            router.replace('/home')
                            location.reload()
                        } else {
                            // router.replace('/home')
                            // location.reload()
                        }
                    })
                }
            }

            // 接收到消息的回调方法
            websocket.onmessage = function(e) {
                console.log('收到', e)
                if (e.target.url.includes('light') && e.data.includes('false')) {
                    var data = JSON.parse(e.data)
                    modalInfo('确定', 'tipsIcon2', '补光灯：' + data.msg, (val) => {
                        // if (val) {
                        // }
                    })
                }
            }
        }
    } else {
        // console.info("当前浏览器 Not support websocket");
        alert('当前浏览器 Not support websocket')
    }
    return callback
}
// websocket 通用关闭方法
export const closeWebSocket = () => {
    // 连接关闭的回调方法
    if (websocket) {
        websocket.close()
        console.log('WebSocket连接关闭lock')
        websocket = null
    }
}
export const sendMsg = (msg) => {
    console.info('msg=' + msg)
    websocket.send(msg)
}
// 暂存模式-开锁
export function tempOpenLock(gh, callback) {
    if (websocket == null || websocket.readyState != 1) {
        modalConfirm('返回首页', '确定', 'tipsIcon2', '柜子通信服务连接失败', (val) => {
            if (val) {
                router.replace('/home')
                location.reload()
            }
        })
        return
    }
    const jsTicks = new Date().getTime()
    const arr = filterGh(gh)
    console.log('gh', gh)
    console.log('arr', arr)
    Vue.prototype.logger({ title: '开柜号编译区别', gh, arr })
    if (arr && arr.length > 0) {
        const text = '{ "actionType": "openlock", "box": "' + arr[0] + '", "door": "' + arr[1] + '", "checkCode": "11" ,"requestTime":"' + jsTicks + '"}'
        sendMsg(text)
        if (callback) {
            callback(websocket, gh)
        }
    }
}
// 暂存模式 - 检测柜子是否关闭完整版
export function tempCheckLock(gh, callback) {
    var jsTicks = new Date().getTime()
    var arr = filterGh(gh)
    if (arr && arr.length > 0) {
        var text = '{ "actionType": "checklock", "box": "' + arr[0] + '", "door": "' + arr[1] + '", "checkCode": "11" ,"requestTime":"' + jsTicks + '"}'
        sendMsg(text)
        callback && callback(websocket)
    }
}
// 保管模式-开锁完整版
export function openLock(gh, callback) {
    if (websocket == null || websocket.readyState != 1) {
        modalConfirm('重新加载', '返回首页', 'tipsIcon2', '柜子通信服务连接失败', (val) => {
            if (val) {
                location.reload()
            } else {
                router.replace('/home')
                location.reload()
            }
        })
    } else {
        var jsTicks = new Date().getTime()
        var arr = deal(gh)
        // console.log(arr, '处理后的柜子信息');
        if (arr && arr.length > 0) {
            var text = '{ "actionType": "openlock", "box": "' + arr[0] + '", "door": "' + arr[1] + '", "checkCode": "11" ,"requestTime":"' + jsTicks + '"}'
            sendMsg(text)
            if (callback) {
                callback(websocket)
            }
        }
    }

    return callback
}
// 检测柜子是否关闭完整版
export function checkLock(gh, callback) {
    if (websocket == null || websocket.readyState != 1) {
        modalConfirm('重新加载', '返回首页', 'tipsIcon2', '柜子通信服务连接失败', (val) => {
            if (val) {
                location.reload()
            } else {
                router.replace('/home')
                location.reload()
            }
        })
    } else {
        var jsTicks = new Date().getTime()

        var arr = deal(gh)

        if (arr && arr.length > 0) {
            var text = '{ "actionType": "checklock", "box": "' + arr[0] + '", "door": "' + arr[1] + '", "checkCode": "11" ,"requestTime":"' + jsTicks + '"}'
            sendMsg(text)
            if (callback) {
                callback(websocket)
            }
        }
    }
    return callback
}

function deal(gh) {
    var serverConfig = JSON.parse(localStorage.getItem('serverConfig'))

    var config = serverConfig.cabinetConfig // 配置文件
    var cabinet = gh // 柜号

    var name = cabinet.slice(0, 1)
    name = name.toUpperCase()
    var num = cabinet.slice(1, cabinet.length)
    num = Number(num[0] == '0' ? num.slice(num.indexOf('0') + 1, num.length) : num)
    const chartCode = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    var index2 = chartCode.indexOf(name)

    const box = config.split(';')
    function data(box, index2) {
        var aaa = 0
        var bbb = ''

        for (var i = 0; i < box.length; i++) {
            const item = box[i]
            if (i <= index2) {
                if (item.indexOf(',') == -1) {
                    aaa += 1
                } else {
                    aaa += 1
                    const arr = item.split(',')
                    if (i == index2) {
                        if (num > Number(arr[0])) {
                            aaa += 1
                        }
                    } else {
                        aaa += 1
                    }
                }
            }
        }

        const item = box[index2]
        if (item) {
            if (item.indexOf(',') == -1) {
                if (num > Number(item)) {
                    modalInfo('返回首页', 'tipsIcon2', '柜号不存在！', (val) => {
                        if (val) {
                            router.replace('/home')
                            location.reload()
                        }
                    })
                    return []
                } else {
                    bbb = num
                }
            } else {
                const arr = item.split(',')
                if (num > Number(arr[0])) {
                    if (num > Number(arr[0]) + Number(arr[1])) {
                        modalInfo('返回首页', 'tipsIcon2', '柜号不存在！', (val) => {
                            if (val) {
                                router.replace('/home')
                                location.reload()
                            }
                        })
                        return []
                    } else {
                        bbb = num - Number(arr[0])
                    }
                } else {
                    bbb = num
                }
            }
        } else {
            modalInfo('返回首页', 'tipsIcon2', '柜号不存在！', (val) => {
                if (val) {
                    router.replace('/home')
                    location.reload()
                }
            })
            return []
        }
        return [aaa, bbb]
    }
    return data(box, index2)
}
// 根据配置文件与传入的box（列），door（行），拿到完整的柜号：例：A01
export function getGHEvent(box, door) {
    door = door + ''
    if (door.indexOf('0') == 0) {
        door = door.substring(1)
        // console.log(door)
    }
    var config = serverConfig.cabinetConfig // 配置文件;
    var boxArr = config.split(';')
    const chartCode = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z']
    const newBoxArr = chartCode.splice(0, boxArr.length)
    var newBoxObjArr = []
    boxArr.forEach((item, index) => {
        if (newBoxObjArr.length > 0) {
            const cols = newBoxObjArr[index - 1].cols
            if (item.indexOf(',') !== -1) {
                var obj = {
                    name: newBoxArr[index],
                    cols: [cols[cols.length > 1 ? 1 : 0] + 1, cols[cols.length > 1 ? 1 : 0] + 2],
                    count: item.split(',')[0]
                }
            } else {
                var obj = {
                    name: newBoxArr[index],
                    cols: [cols[0] + 1]
                }
            }
        } else {
            if (item.indexOf(',') !== -1) {
                var obj = {
                    name: newBoxArr[index],
                    cols: [index + 1, index + 2],
                    count: item.split(',')[0]
                }
            } else {
                var obj = {
                    name: newBoxArr[index],
                    cols: [index + 1]
                }
            }
        }
        newBoxObjArr.push(obj)
    })
    let gh = ''
    newBoxObjArr.forEach((item) => {
        if (item.cols.indexOf(Number(box)) == 0) {
            gh = item.name + (Number(door) < 10 ? '0' + door : door)
        }
        if (item.cols.indexOf(Number(box)) == 1) {
            const newDoor = Number(door) + Number(item.count) < 10 ? '0' + (Number(door) + Number(item.count)) : Number(door) + Number(item.count)
            gh = item.name + newDoor
        }
    })
    return gh
}

export function parseTime(time, cFormat) {
    if (arguments.length === 0) {
        return null
    }
    const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
    let date
    if (typeof time === 'object') {
        date = time
    } else {
        if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
            time = parseInt(time)
        }
        if (typeof time === 'number' && time.toString().length === 10) {
            time = time * 1000
        }
        date = new Date(time)
    }
    const formatObj = {
        y: date.getFullYear(),
        m: date.getMonth() + 1,
        d: date.getDate(),
        h: date.getHours(),
        i: date.getMinutes(),
        s: date.getSeconds(),
        a: date.getDay()
    }
    const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
        let value = formatObj[key]
        if (key === 'a') {
            return ['日', '一', '二', '三', '四', '五', '六'][value]
        }
        if (result.length > 0 && value < 10) {
            value = '0' + value
        }
        return value || 0
    })
    return time_str
}
// 查询当前的操作系统
export const GetOSInfo = () => {
    var _pf = navigator.platform
    if (String(_pf).indexOf('Linux') > -1) {
        return true
    } else {
        return false
    }
}
export function compareRfid(rightrfids, onlinerfids) {
    if (rightrfids.length !== onlinerfids.length) {
        return false
    } else {
        for (let i = 0; i < rightrfids.length; i++) {
            if (!onlinerfids.includes(rightrfids[i])) {
                return false
            }
        }
    }
    return true
}
// ----------------------------------------------
// 语音提示 播放
// flag:true 播放，false 暂停
var synth = speechSynthesis
var msg = new SpeechSynthesisUtterance()
export function voiceBroadcast(flag, text, name) {
    var isLinux = GetOSInfo()
    if (isLinux) {
        var audio = document.getElementById('audio')
        audio.src = './uos-umt-audio/' + name + '.mp3'
        if (flag) {
            setTimeout(() => {
                audio.play()
            }, 1000)
            // 以下为备用方案，因audio标签加载资源前开始播放存在缺少音频问题
            // axios({
            //     url: './uos-umt-audio/' + name + '.mp3',
            //     method: 'get',
            //     responseType: 'blob'
            // }).then((res) => {
            //     const blob = new Blob([res.data])
            //     audio.src = URL.createObjectURL(blob)
            //     setTimeout(() => {
            //         audio.play()
            //     }, 1000)
            // })
        } else {
            audio && audio.pause()
        }
    } else {
        if (flag) {
            msg.text = text
            msg.lang = 'zh-CN'
            msg.volume = '1'
            msg.rate = 1.5
            msg.pitch = 1
            synth.speak(msg)
        } else {
            msg.text = ''
            msg.lang = 'zh-CN'
            synth.cancel(msg)
        }
    }
}
let debounceTimer
// 防抖
export function debounce(fn, delay) {
    return (function() {
        if (debounceTimer) {
            clearTimeout(debounceTimer)
        }
        debounceTimer = setTimeout(() => {
            debounceTimer = null
            fn()
        }, delay || 1000)
    })()
}
// base64转file
export function base64ToFile(base64, fileName) {
    var arr = base64.split(',')
    var mime = arr[0].match(/:(.*?);/)[1]
    var bstr = atob(arr[1])
    var n = bstr.length
    var u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], fileName, { type: mime })
}
// 网络错误500
export const dealOnError = () => {
    modalConfirm('返回首页', '确定', 'tipsIcon2', '数据请求错误！',
        (val) => {
            if (val) {
                removeNewToken()
                store.commit('removeUser')
                router.replace('/home')
                location.reload()
            } else {
                console.log('忽略')
            }
        },
        true
    )
}
// 401权限过去判断
export const dealOn401 = (tips = '会话已过期！', duration = 15) => {
    const gDialog = GxxDialogConfirm({
        showHeader: false,
        showHeaderClose: false,
        showFooter: false,
        component: dealDialog,
        componentParams: {
            iconType: 'error',
            duration,
            text: tips,
            okText: '返回首页'
        }
    })
    gDialog.on.then((res) => {
        removeNewToken()
        store.commit('removeUser')
        router.replace('/home')
    })
}
