import LightWebsoket from './websoket/index'
import Lock from '@/libs/lock/index'
import Vue from 'vue'
import { logger } from '@/libs/log'
const { EventEmitter } = require('events')
const ipc<PERSON>enderer = window.electronAPI?.ipcRenderer || null

const errorCode = {
	0: '配置错误',
	1: '参数异常'
}

class Light extends EventEmitter {
	constructor() {
		super()
		this.lightObj = null
		this.systemConfig = {}
	}
	/**
	 * 初始化
	 * @returns
	 */
	init() {
		this.systemConfig = Vue.prototype.$store.getters.getTerminalConfig
		if (!this.manufacturerValid() || !this.lightComValid() || !this.lightControlProtocolValid()) {
			return
		}
		if (this.systemConfig.lightControlProtocol == 1) {
			const regex = /^(ws:\/\/\d+\.\d+\.\d+\.\d+:)/
			const match = this.systemConfig.lightCom.match(regex)
			if (match && match[1]) {
				this.lightObj = new LightWebsoket(this.systemConfig.lightCom, (type, data) => {
					this.emit(type, this.handleData(data))
				})
			} else {
				logger(`light: LightWebsoket 正则校验出错，当前配置为：${this.systemConfig.lightCom}`)
				this.emit(
					'error',
					this.handleData({
						code: 0,
						msg: errorCode[0]
					})
				)
			}
		} else {
			if (this.systemConfig.lightType == 3) {
				this.lightObj = new Lock()
				console.log('DC补光灯初始化',this.lightObj)
				let num = 0 // 计算补光灯指令回调次数
				this.lightObj.on('open', (data) => {
					console.log('open', data)
					logger('open', data)
					if (data.code != 0) {
						logger(`light: this.systemConfig.lightType=${this.systemConfig.lightType},补光灯open出错，当前配置为：${JSON.stringify(data)}`)
					}
					this.emit('open', this.handleData(data))
				})
				this.lightObj.on('message', (data) => {
					console.log('message', data)
					if (data.actionType == 'checklight') {
						num++
						if (num >= 2) {
							num = 0
							this.emit('message', this.handleData(data))
						}
					}
				})
				this.lightObj.on('error', (data) => {
					logger(`light: this.systemConfig.lightType=${this.systemConfig.lightType},补光灯error出错，当前配置为：${JSON.stringify(data)}`)
					num = 0
					this.emit('error', this.handleData(data))
				})
				this.lightObj.init()
			} else {
				this.communicate()
			}
		}
	}
	/**
	 * 操作发送指令
	 * @returns
	 */
	communicate() {
		ipcRenderer.send('handle-light', { type: 'init' })
		ipcRenderer.on('light-callback-result', (event, arg) => {
			if (arg.type == 'open') {
				this.lightObj = arg.code == 0
			}
			this.emit(arg.type, this.handleData(arg))
		})
	}
	/**
	 * 销毁连接
	 * @returns
	 */
	colseCommunicate() {
		ipcRenderer.send('handle-light', { type: 'close' })
		ipcRenderer.removeAllListeners('light-callback-result')
	}
	/**
	 * 操作
	 * @param {String} actionType 操作类型 openlight closelight
	 * @returns
	 */
	handle(actionType, box, door) {
		if (!this.linkValid() || !this.actionTypeValid(actionType) || !this.manufacturerValid() || !this.lightControlProtocolValid()) {
			return
		}
		if (this.systemConfig.lightControlProtocol == 1) {
			this.lightObj.send(actionType)
		} else {
			if (this.systemConfig.lightType == 3) {
				this.lightObj.handleLight(actionType, box, door)
			} else {
				ipcRenderer.send('handle-light', { type: actionType })
			}
		}
	}
	/**
	 * 处理数据
	 * @param {Object} arg
	 * @returns Object
	 */
	handleData(arg) {
		const newData = {
			code: arg.code,
			msg: arg.msg
		}
		return newData
	}
	/**
	 * 销毁事件监听
	 */
	removeAllEventListeners() {
		this.removeAllListeners('open')
		this.removeAllListeners('close')
		this.removeAllListeners('message')
		this.removeAllListeners('error')
	}
	/**
	 * 销毁
	 * @returns
	 */
	destroyed() {
		this.removeAllEventListeners()
		if (this.systemConfig.lightType == 3) {
			this.lightObj && this.lightObj.destroyed()
		} else if (this.systemConfig.lightType == 1) {
			if (this.systemConfig.lockControlProtocol == 1) {
				this.lightObj && this.lightObj.close()
				return
			}
		}
		ipcRenderer.removeAllListeners('light-callback-result')
		if (!this.linkValid(false)) {
			this.lightObj = null
			return
		}
		this.colseCommunicate()
		this.lightObj = null
	}
	/**
	 * 补光灯协议校验
	 */
	lightControlProtocolValid() {
		if ([1, 2].includes(Number(this.systemConfig.lightControlProtocol))) {
			return true
		}
		logger(`light: 补光灯协议校验出错，当前配置为：${this.systemConfig.lightControlProtocol}`)
		this.emit(
			'error',
			this.handleData({
				code: 0,
				msg: errorCode[0]
			})
		)
		return false
	}
	/**
	 * 连接状态校验
	 * @param {Boolean} isReturn 是否回调
	 * @returns Boolean
	 */
	linkValid(isReturn = true) {
		if (!this.lightObj) {
			logger('light: 连接状态校验出错，当前未连接状态')
			isReturn &&
				this.emit(
					'error',
					this.handleData({
						code: 0,
						msg: errorCode[0]
					})
				)
			return false
		}
		return true
	}
	/**
	 * 补光灯COM校验
	 * @returns Boolean
	 */
	lightComValid() {
		if (!this.systemConfig.lightCom) {
			logger('light: 补光灯COM校验出错，当前未配置com')
			this.emit(
				'error',
				this.handleData({
					code: 0,
					msg: errorCode[0]
				})
			)
			return false
		}
		return true
	}
	/**
	 * 厂商配置校验
	 * @returns Boolean
	 */
	manufacturerValid() {
		if ([1, 2, 3].includes(Number(this.systemConfig.lightType))) {
			return true
		}
		logger(`light: 厂商配置校验出错，当前配置为：${this.systemConfig.lightType}`)
		this.emit(
			'error',
			this.handleData({
				code: 0,
				msg: errorCode[0]
			})
		)
		return false
	}
	/**
	 * 操作类型校验
	 * @param {String} actionType 操作类型 openlight closelight
	 * @returns Boolean
	 */
	actionTypeValid(actionType) {
		if (['openlight', 'closelight'].includes(actionType)) {
			return true
		}
		logger(`light: 操作类型校验出错，当前配置为：${actionType}`)
		this.emit(
			'error',
			this.handleData({
				code: 1,
				msg: errorCode[1]
			})
		)
		return false
	}
}

export default Light
