<template>
    <div id="app">
        <router-view v-if="isRouterAlive"></router-view>
    </div>
</template>

<script>
import configComponent from '@/components/setConfig'
import CountDown from '@/libs/countdowm.js'
import { getToken } from '@/libs/utils'
import Normal from '@/router/normal.js'
import Temporary from '@/router/temporary.js'
import { lockTypeList } from '@/utils/common_dict'
import { getMediaDevices } from '@/utils/normal_util'
import { aesDecrypt } from '@/views/mqtt/auth'
import { setToken } from '@/libs/utils.js'

const ipcRenderer = window.electronAPI?.ipcRenderer || null
export default {
    name: 'App',
    provide() {
        return {
            reload: this.reload,
            CountDown: new CountDown(),
            updateAppInfo: this.updateAppInfo
        }
    },
    data() {
        return {
            isRouterAlive: true,
            dialogObj: null,
            devicesList:[]
        }
    },
    computed: {
        config() {
            return this.$store.getters['getTerminalConfig'] || {}
        }
    },
    watch: {
        // 路由不在首页向终端发送正在操作指令
        '$route.name'(name) {
            // if (!ipcRenderer) return
            // ipcRenderer.send('set-terminal-status', name != 'home')
        }
    },
    created() {
        this.initIpc()
    },
    // mounted(){
    //     async getDevices() {
	// 		this.devicesList = []
	// 		const devices = await getMediaDevices()
	// 		this.devicesList = devices || []
	// 	}
    // },
    beforeDestroy() {
        this.closeConfigDialog()
    },
    methods: {
        initIpc() {
            if (!ipcRenderer) {
                return
            }
            const systemConfig = ipcRenderer.sendSync('getTermConfig')
            // this.setStoreConfig(systemConfig)
            const isMain = ipcRenderer.sendSync('is-main-window')
            if (!isMain) {
                return
            }
            // console.log(systemConfig,'systemConfig')
            //  this.updateConfig(systemConfig)
            this.initAppInfo(systemConfig)
            ipcRenderer.on('aes-decrypt', (event, params) => {
                ipcRenderer.send('aes-decrypt-result', aesDecrypt(params.authString, params.aesKey))
            })
            // 主进程获取配置信息
            ipcRenderer.on('get-local-config', async(event, options) => {
                let cameraList = []
                if (options && options.isGetDevices) {
                    cameraList = await getMediaDevices()
                    console.log(cameraList,'cameraList')
                }
                ipcRenderer.send('get-local-config-result', JSON.stringify({ config: this.config, cameraList }))
            })
            // 主进程设置终端配置
            ipcRenderer.on('set-local-config', (event, config) => {
                try {
                    this.logger('接收的配置数据类型：', typeof config)
                    Object.assign(this.config, JSON.parse(config))
                    this.updateAppInfo(this.config)
                } catch (e) {
                    this.logger('主进程接收应用配置数据后，发送给渲染层异常', e)
                }
            })
            // 主进程设置终端网络配置
            ipcRenderer.on('set-network-config', (event, config) => {
                try {
                    Object.assign(this.config, JSON.parse(config))
                    this.updateAppInfo(this.config, false)
                } catch (e) {
                    this.logger('主进程接收网络配置数据后，发送给渲染层异常', e)
                }
            })
            // 获取属性信息
            ipcRenderer.on('get-property', async() => {
                this.logger('=====柜子统计信息开始======')
                const name = this.config.productName
                const _token = getToken()
                let total = 0
                let free = 0
                if (_token) {
                    this.logger('=====柜子上报请求======')
                    const ret = await this.getStatisticsInfo()
                    total = ret.total
                    free = ret.free
                }
                const model = lockTypeList.filter((item) => item.value == this.config.lockType)[0].label
                ipcRenderer.send('get-property-result', { name, idleBox: free, totalBox: total, model })
            })
        },
        initAppInfo(config) {
            this.setStoreConfig(config)
            // debugger
            // if (!config.cabinetCode || !config.server) {
            //     this.openConfigDialog()
            //     return
            // }
            if (config.serverVersion == 5) {
                this.getToken(config)
                // this.getGlobalConfig(config)
            } else {
                this.updateConfig(config)
            }
        },
        updateAppInfo(config, isReload = true) {
            this.writeConfigFile(config)
            this.setStoreConfig(config)
            if (isReload) {
                sessionStorage.clear()
                window.location.reload()
            }
        },
        setStoreConfig(config) {
            this.logger('网页设置的配置', config)
            this.$store.commit('setTerminalConfig', config)
        },
        writeConfigFile(config) {
            ipcRenderer && ipcRenderer.send('setTermConfig', config)
        },
        // 初始化人脸配置
        initFaceConfig(systemConfig) {
            if (systemConfig.photoshopMode != 0) {
                ipcRenderer.send('init_face_config', { photoshopMode: systemConfig.photoshopMode })
            } else {
                ipcRenderer.send('destory_face_detector')
            }
        },
        reload() {
            const that = this
            that.isRouterAlive = false
            that.$nextTick(function() {
                that.isRouterAlive = true
            })
        },
        closeConfigDialog() {
            this.dialogObj && this.dialogObj.close()
            this.dialogObj = null
        },
        openConfigDialog() {
            if (this.dialogObj) {
                return
            }
            this.dialogObj = this.$GxxDialog({
                title: '系统配置',
                showHeader: true,
                showHeaderClose: false,
                component: configComponent,
                componentParams: { isWeb: false }
            })
            this.dialogObj.on.then((res) => {
                this.dialogObj = null
                if (res.type == 'confirm') {
                    this.updateAppInfo(res.data)
                } else {
                    ipcRenderer && ipcRenderer.send('close')
                }
            })
        },
        getToken(config) {
			const params = {
				grant_type: 'client_credentials',
				client_id: this.serverConfig?.clientName || '',
				client_secret: this.serverConfig?.clientPwd || ''
			}
			this.$http.bsp.getOauthToken(params).then((res) => {
				if (res) {
					setToken(res.access_token)
					this.getGlobalConfig(config)
					// this.getCabinetSide()
					// this.getCabinetBox()
				}
			})
		},
        // 获取线上配置
        getGlobalConfig(systemConfig) {
            this.$Get(this.AmtCom.getCabinetISetting, { ip: this.serverConfig.iotAddress  || this.serverConfig.ip })
                .then((res) => {
                    console.log('getGlobalConfiggetGlobalConfig',res)
                    if (res.success) {
                        const config = { ...systemConfig, ...res.data }
                        // console.log(config,'configconfigconfig')
                        this.updateConfig(config)
                    } else {
                        this.openErrorPrompt('服务配置错误，请联系管理员！')
                    }
                })
                .catch((err) => {
                    console.log(err)
                    this.openErrorPrompt('服务错误(404)，请联系管理员！')
                })
        },
        openErrorPrompt(message) {
            this.$GxxPrompt.info({
                type: 'error',
                rightBtnText: '修改配置',
                leftBtnText: '退出',
                message,
                callback: (res) => {
                    if (res.type === 'rightBtn') {
                        // this.openConfigDialog()
                    } else {
                        ipcRenderer && ipcRenderer.send('close')
                    }
                }
            })
        },
        // 更新配置
        updateConfig(config) {
            this.logger(config,'=====柜子统计信息开始======',)
            this.setStoreConfig(config)
            this.handleInitMode(config)
            this.initFaceConfig(config)
        },
        // 根据配置是否当前是暂存模式/保管模式
        handleInitMode(systemConfig) {
            const modeMap = {
                0: Normal,
                1: Temporary
            }
            console.log( systemConfig.patternType,' this.$router this.$router')

            this.$router.addRoute(...modeMap[systemConfig.patternType])
            console.log( this.$router,systemConfig.patternType,' this.$router this.$router')
            let tt = setTimeout(() => {
                clearTimeout(tt)
                tt = null
                this.$router.push({ name: 'home' })
            }, 500)
        },
        // 获取统计信息
        getStatisticsInfo() {
            return new Promise((resolve, reject) => {
                let total = 0
                let free = 0
                const params = {
                    cabinetCode: this.serverConfig?.cabinetCode,
                    centerId: this.serverConfig?.centerId,
                    searchObj: '{}',
                    deviceType: ''
                }
                this.$Post(this.PymApi.getStatisticsInfo, params)
                    .then((res) => {
                        if (res.success) {
                            total = res.total || 0
                            free = res.free || 0
                        }
                    })
                    .catch(() => {
                        total = 0
                        free = 0
                    })
                    .finally(() => {
                        resolve({ total, free })
                    })
            })
        }
    }
}
</script>

<style lang="less">
@import url('./assets/css/reset.css');
@import url('./assets/css/styles/iview.css');
@import url('./assets/css/common.less');
@import url('./assets/css/scanCode.css');
.size {
	width: 100%;
	height: 100%;
	background: #ebf5ff;
}
html,
body {
	.size;
	overflow: auto;
	margin: 0;
	padding: 0;
}
#app {
	.size;
}
.ivu-message {
	.ivu-message-custom-content {
		i {
			font-size: 32px;
		}
		span {
			font-size: 24px !important;
		}
	}
}
</style>
<style lang="less" scoped>
.error-box {
	width: 400px;
	height: 220px;
	border-radius: 8px;
	overflow: hidden;
	background: #fff;
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	.tips-box {
		background: #fff;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		.icon {
			width: 60px;
			height: 60px;
			margin: 20px 0;
		}
		.text {
			font-size: 18px;
		}
	}
	.btn {
		width: 80px;
		height: 30px;
		line-height: 30px;
		text-align: center;
		background: #0099e6;
		color: #fff;
		font-size: 14px;
		margin: 30px auto 0;
	}
}
</style>
