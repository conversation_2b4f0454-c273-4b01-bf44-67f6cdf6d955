import { $Post } from '../fetch'
const prefixV3 = '/bsp-uac' //'/sundun-edas'
const prefixV5 = '/bsp-uac'

const urlMap = {
    getOauthToken: {
        v3: `${prefixV3}/oauth/token`,
        v5: `${prefixV5}/oauth/token`
    },
    getOrgByUserData: {
        v3: `${prefixV3}/priv/user/getUserByOrgCodes`,
        v5: `${prefixV5}/api/v1/userorg/user/getOrgUserPageData`
    },
    getRuleSql: {
        v3: '',
        v5: `${prefixV5}/api/v1/perm/perm/getRuleSql`
    },
    logout: {
        v3: `${prefixV3}/oauth/logout`,
        v5: `${prefixV5}/oauth/logout`
    }
}
const apiMethodsMap = {
    getOauthToken(apiUrl) {
        console.log(apiUrl, '=2=')
        return (data) => $Post(apiUrl, data)
    },
    getOrgByUserData(apiUrl) {
        return (data) => $Post(apiUrl, data)
    },
    getRuleSql(apiUrl) {
        return (data) => {
            if (apiUrl) {
                return $Post(apiUrl, data)
            } else {
                return new Promise((resolve, reject) => {
                    resolve(false)
                })
            }
        }
    },
    logout(apiUrl) {
        return (data) => $Post(apiUrl, data)
    }
}
export default {
    urlMap,
    apiMethodsMap
}
