import request from './request'
import { getToken } from '@/libs/utils'

const baseURL = setAxiosConfig()

function setAxiosConfig() {
	const systemConfig = JSON.parse(localStorage.getItem('serverConfig') || '{}')
	return systemConfig.server || ''
}

export function $Get(url, params = {}) {
	return request({
		baseURL,
		url,
		method: 'get',
		params
	})
}
// formData格式
export function $Post(url, data, hideLoad) {
	return request({
		baseURL,
		url,
		method: 'post',
		data
	})
}
// formData格式
export function $Post_SH(url, data, hideLoad) {
	return request({
		baseURL,
		url,
		method: 'post',
		data
	})
}
// formData格式
export function $Post_Json(url, data, hideLoad) {
	return request({
		baseURL,
		url,
		method: 'post',
		params:{
			access_token:getToken()
		},
		data,
		headers: {
        'Content-Type': 'application/json',
    }
	})
}

// 文件上传接口
export function $Upload(url, data, hideLoad) {
	return request({
		baseURL,
		url,
		method: 'post',
		data,
		headers: {
			'Content-Type': 'multipart/form-data'
		}
	})
}
