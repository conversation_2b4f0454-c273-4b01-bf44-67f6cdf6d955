#!/bin/bash

#删除开始菜单快捷方式
if echo remove DESKTOPFILE | grep APPNAME
then
    rm -f DESKTOPFILE  #加一层保护防止误删系统目录
fi

#删除桌面快捷方式
if echo remove DESKTOPLINK | grep APPNAME
then
    rm -f DESKTOPLINK  #加一层保护防止误删系统目录
fi

#删除自启动文件
if [ -f "$HOME/.config/autostart/APPNAME.desktop" ]; then
    rm -f "$HOME/.config/autostart/APPNAME.desktop"
fi

#退出进程APPNAME
result=`ps -ef | grep 'INSTALLDIR/APPNAME' | grep -v "grep"`
if [ -n "${result}" ];then
    killall -9 APPNAME
fi

#删除安装目录
installDirResult=`ls INSTALLDIR`
if [ -n "${installDirResult}" ];then
    rm -rf INSTALLDIR
fi

#删除桌面快捷方式
for dir in /home/<USER>
    desktop_file=$dir/Desktop/APPNAME.desktop
    if [ -f "${desktop_file}" ]; then
        rm -f "${desktop_file}"
    fi
    desktop_file_zh_CN=$dir/桌面/APPNAME.desktop
    if [ -f "${desktop_file_zh_CN}" ]; then
        rm -f "${desktop_file_zh_CN}"
    fi
done

