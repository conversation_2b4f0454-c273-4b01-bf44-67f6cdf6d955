#!/bin/bash

ret=`find INSTALLDIR -name APPNAME 2> /dev/null`
if [ -n "${ret}" ]; then
    sudo udevadm control --reload-rules
    for dev in $(ls /sys/class/tty); do sudo udevadm trigger --name-match=$dev; done
fi
#sudo udevadm trigger -S drm
echo "创建桌面快捷方式"
for dir in /home/<USER>
    desktop_dir=$dir/Desktop
    if [ -d "${desktop_dir}" ]; then
        cp -f DESKTOPFILE ${desktop_dir}/APPNAME.desktop
    fi
    desktop_dir_zh_CN=$dir/桌面
    if [ -d "${desktop_dir_zh_CN}" ]; then
        cp -f DESKTOPFILE ${desktop_dir_zh_CN}/APPNAME.desktop
    fi
done

result=`uname -a | grep aarch64`
if [ "$result" != "" ]; then
    su gosuncn -c "nohup INSTALLDIR/APPNAME.sh &"
else
    INSTALLDIR/APPNAME.sh &
fi
