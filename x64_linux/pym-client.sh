#!/bin/sh

#安装路径
CURRENT_DIR=$(dirname `readlink -f $0`)

# 获取CPU架构
cpu_arch=$(uname -m)
#console.log(cpu_arch,'cpu_archcpu_archcpu_arch')
# 初始化LD_LIBRARY_PATH为空，以防之前已经有设置
LD_LIBRARY_PATH=""

if [ "$cpu_arch" = "x86_64" ]; then
	LD_LIBRARY_PATH="${CURRENT_DIR}/resources/extraResources/face_sdk/amd64_new:${CURRENT_DIR}/resources/extraResources/face_sdk/amd64_new/include:${CURRENT_DIR}/resources/extraResources/face_sdk/amd64_new/models:$LD_LIBRARY_PATH"
elif [ "$cpu_arch" = "aarch64" ]; then
	LD_LIBRARY_PATH="${CURRENT_DIR}/resources/extraResources/face_sdk/arm64_new:${CURRENT_DIR}/resources/extraResources/face_sdk/arm64_new/include:${CURRENT_DIR}/resources/extraResources/face_sdk/arm64_new/models:$LD_LIBRARY_PATH"
else
	echo "Unsupported CPU architecture: $cpu_arch"
	exit 1
fi

# 导出LD_LIBRARY_PATH环境变量
#console.log(LD_LIBRARY_PATH,'LD_LIBRARY_PATHLD_LIBRARY_PATHLD_LIBRARY_PATH')
export LD_LIBRARY_PATH

# 启动程
${CURRENT_DIR}/pym-client
