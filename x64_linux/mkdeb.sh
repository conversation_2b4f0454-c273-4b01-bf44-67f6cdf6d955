#!/bin/bash

# 配置项 BEGIN ---------------------------------------------
# CPU架构类型
CPU_ARCH=$3
# 免安装包目录
PROJ_ROOT=../$4
# deb打包文件路径
PROJ_MKPKG_DIR=../x64_linux
#指定安装目录
APP_INSTALL_DIR=/opt/gosuncn/$2
# 应用名称
APP_NAME=$2
# 版本号
VERSION=$1

# 配置项 END ---------------------------------------------

CURRENT_DIR=$(dirname `readlink -f $0`)
PROJ_RELEASE_DIR=${CURRENT_DIR}/release  #确保release目录结尾，防止误删系统目录

echo "指定包文件名" &&
APP_DEBIAN_NAME=${APP_NAME}_${CPU_ARCH}_${VERSION}.deb

echo "删除release目录" &&
if [ -d "${PROJ_RELEASE_DIR}/../release" ]; then
	rm -rf ${PROJ_RELEASE_DIR}/../release
fi &&

echo "重新创建release目录" &&
mkdir -p ${PROJ_RELEASE_DIR} &&
cp -rf ${PROJ_ROOT}/* ${PROJ_RELEASE_DIR} &&

desktop=`cat $HOME/.config/user-dirs.dirs | grep DESKTOP | tail  -1  |cut -d '=' -f 2  | sed 's/\"//g'` &&
desktop=`eval echo $desktop` &&

echo "删除DEBIAN目录" &&
if [ -d "${PROJ_MKPKG_DIR}/DEBIAN" ]; then
	rm -rf ${PROJ_MKPKG_DIR}/DEBIAN
fi &&

echo "重新创建DEBIAN目录" &&
mkdir ${PROJ_MKPKG_DIR}/DEBIAN &&

echo "生成control脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/control.template | sed "s/VERSION/${VERSION}/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/CPUARCH/${CPU_ARCH}/g" > ${PROJ_MKPKG_DIR}/DEBIAN/control &&

echo "生成postinst文件" &&
cat ${PROJ_MKPKG_DIR}/scripts/postinst.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/postinst &&

echo "生成prerm脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/prerm.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/prerm &&

echo "生成postrm脚本" &&
cat ${PROJ_MKPKG_DIR}/scripts/postrm.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/DESKTOPFILE/\\/usr\\/share\\/applications\\/${APP_NAME}.desktop/g" | sed "s/APPNAME/${APP_NAME}/g" | sed "s/DESKTOPLINK/${desktop////\\/}\\/${APP_NAME}.desktop/g" > ${PROJ_MKPKG_DIR}/DEBIAN/postrm &&

echo "按DPKG打包命令，创建运行目录结构" &&
mkdir -p ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR} &&
mv ${PROJ_RELEASE_DIR}/* ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR} &&
mv ${PROJ_RELEASE_DIR}/..${APP_INSTALL_DIR:0:`expr index ${APP_INSTALL_DIR:1} "/"`} ${PROJ_RELEASE_DIR} &&

echo "按DPKG打包命令，创建DEBIAN目录" &&
chmod 0755 -R ${PROJ_MKPKG_DIR}/DEBIAN &&
mv ${PROJ_MKPKG_DIR}/DEBIAN ${PROJ_RELEASE_DIR} &&

echo "使能直接命令行运行" &&
mkdir -p ${PROJ_RELEASE_DIR}/usr/bin &&
cd ${PROJ_RELEASE_DIR}/usr/bin &&
ln -s ${APP_INSTALL_DIR}/${APP_NAME}.sh ${APP_NAME} &&

echo "使能创建开始菜单快捷方式" &&
mkdir -p ${PROJ_RELEASE_DIR}/usr/share/applications &&
cat ${PROJ_RELEASE_DIR}/../scripts/${APP_NAME}.desktop.template | sed "s/INSTALLDIR/${APP_INSTALL_DIR////\\/}/g" | sed "s/APPNAME/${APP_NAME}/g" > ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop &&
chmod +x ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop &&
sudo chown root ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}/chrome-sandbox &&
sudo chmod 4755 ${PROJ_RELEASE_DIR}${APP_INSTALL_DIR}/chrome-sandbox &&

echo "使能创建桌面快捷方式" &&
mkdir -p ${PROJ_RELEASE_DIR}/${desktop} &&
cd ${PROJ_RELEASE_DIR}/${desktop} &&
cp -f ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop ${APP_NAME}.desktop &&

echo "使能开机自启动" &&
mkdir -p ${PROJ_RELEASE_DIR}/etc/xdg/autostart &&
cp -f ${PROJ_RELEASE_DIR}/usr/share/applications/${APP_NAME}.desktop ${PROJ_RELEASE_DIR}/etc/xdg/autostart/${APP_NAME}.desktop &&

mkdir -p ${PROJ_RELEASE_DIR}/etc/udev/rules.d &&
echo "拷贝.rules文件 到 /etc/udev/rules.d目录下" &&
cd ${PROJ_RELEASE_DIR}/../${PROJ_MKPKG_DIR} &&
cp -f ${APP_NAME}.rules ${PROJ_RELEASE_DIR}/etc/udev/rules.d &&

echo "开始打包" &&
cd ${PROJ_RELEASE_DIR}/.. &&
dpkg -b ${PROJ_RELEASE_DIR} ${APP_DEBIAN_NAME} &&

echo "打包完成"
