apiVersion: apps/v1
kind: Deployment
metadata:
  name: pym-ict-nginx
  namespace: test
  labels:
    app: pym-ict-nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pym-ict-nginx
  revisionHistoryLimit: 5
  progressDeadlineSeconds: 600
  minReadySeconds: 0
  strategy:
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  paused: false
  template:
    metadata:
      name: pym-ict-nginx
      labels:
        app: pym-ict-nginx
    spec:
      containers:
      - name: pym-ict-nginx
        image: 10.89.12.33/test/pym-ict-nginx:v1.0.0
        imagePullPolicy: Always
        volumeMounts:
        - name: pym-nginx-config
          mountPath: /etc/nginx/
        command:
        args:
        workingDir:
        ports:
        - containerPort: 8841
        env:
        - name: esp
          value:
        securityContext: {}
        stdin: true
        stdinOnce: true
        tty: true
      volumes:
      - name: pym-nginx-config
        configMap:
          name: pym-nginx-config
      shareProcessNamespace: true
      nodeSelector:
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
---
apiVersion: v1
kind: Service
metadata:
  name: pym-ict-nginx-svc
  namespace: test
  labels:
    app: pym-ict-nginx
spec:
  type: NodePort
#  clusterIP: **********
  ports:
  - name: pym-ict-nginx
    protocol: TCP
    nodePort: 8841
    port: 8841
    targetPort: 8841
  selector:
    app: pym-ict-nginx
