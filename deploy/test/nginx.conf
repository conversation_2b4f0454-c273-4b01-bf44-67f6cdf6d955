
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

	
	server {
        listen       8841;
        server_name  localhost;
		client_max_body_size 1024M;
		location / {
            root  /usr/share/nginx/html/dist;
            index  index.html;
        }
        location /pym-ict {
	        add_header Cache-Control no-cache;
            add_header Pragma no-cache;
            add_header Expires 0;
            alias  /usr/share/nginx/html/pym-ict;
            index  index.html;
        }
	    location /sundun-face {
            proxy_pass   http://localhost:5000/;
        }

        location /pym-app {
	        add_header Cache-Control no-cache;
            add_header Pragma no-cache;
            add_header Expires 0;
            alias  /usr/share/nginx/html/pym-app;
            index  index.html;
        }
		location /pym-sys {
            proxy_pass   http://pym-master-svc5-0-2:8190/;
        }
		location /pym-bus {
            proxy_pass   http://pym-master-svc5-0-2:8190/;
        }
		location /sundun-zfpt {
            proxy_pass   http://pym-zfpt-master-svc5-0-2:28091/;
        }
		location ^~/pym-upload/ {
            proxy_pass   http://***********:8080/;
        }
        location /bsp-bpm {
        	proxy_pass http://bsp-bpm-svc2-1-0:1911/;
        }
        # location /bsp-uac {
        # 	proxy_pass http://bsp-master-svc2-1-0:1910/;
        # }
        location /bsp-uac {
        	proxy_pass http://*************:1910/;
        }
        location /amt-com {
        	proxy_pass http://*************:9400/;
        }
        location /bsp-com {
        	proxy_pass http://bsp-master-svc2-1-0:1910/;
        }
        location /third-zfpt-edas{
	        proxy_pass http://***********:1999/;
	    }
        location /third-zfpt-bus{
        	proxy_pass http://***********:1999/;
        }
    }


    # another virtual host using mix of IP-, name-, and port-based configuration
    #
    #server {
    #    listen       8000;
    #    listen       somename:8080;
    #    server_name  somename  alias  another.alias;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}


    # HTTPS server
    #
    #server {
    #    listen       443 ssl;
    #    server_name  localhost;

    #    ssl_certificate      cert.pem;
    #    ssl_certificate_key  cert.key;

    #    ssl_session_cache    shared:SSL:1m;
    #    ssl_session_timeout  5m;

    #    ssl_ciphers  HIGH:!aNULL:!MD5;
    #    ssl_prefer_server_ciphers  on;

    #    location / {
    #        root   html;
    #        index  index.html index.htm;
    #    }
    #}

}
