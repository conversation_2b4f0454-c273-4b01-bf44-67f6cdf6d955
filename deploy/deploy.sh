### 部署到演示环境

DIR=/app/k8s/pym/pym-ict
SOURCE_DIR="$3"
deploy_env="$1"
DEPLOY_DIR=/opt/yaml/pym/pym-ict
DEPLOY_CM=pym-nginx-config
K8MASTER="$2"
ENV_NAME=${deploy_env}
NS=${ENV_NAME}

CTROL_NAME=pym-ict-nginx
KUBECTL=/opt/kubernetes/bin/kubectl # dev  test
KUBECTL_PROD=/app/kubernetes/bin/kubectl #prod

### 打包构建
build_icp_web(){
  npm install 
  npm install python
  npm install electron 
  npm install ffi-napi -d
  cd ${SOURCE_DIR} && source /etc/profile && npm run build
}

### copy 文件到部署目录
cp_icp_web(){
  rm -rf ${DIR} && mkdir -p ${DIR}/dist && cp -rf ${SOURCE_DIR}/dist ${DIR} && \
  cp -r ${SOURCE_DIR}/deploy/Dockerfile ${DIR}/ && \
  cp -r ${SOURCE_DIR}/deploy/${deploy_env}/* ${DIR}/
}
### build 部署镜像
build_image(){
  cd ${DIR}
  docker build -t 10.89.12.33/${deploy_env}/pym-ict-nginx:v1.0.0 .
  docker push 10.89.12.33/${deploy_env}/pym-ict-nginx:v1.0.0
  cd ${DIR}
  ssh root@${K8MASTER} "mkdir -p ${DEPLOY_DIR}/pym-web-config/"
  scp nginx.conf mime.types  root@${K8MASTER}:${DEPLOY_DIR}/pym-web-config/
  scp pym-ict-nginx.yaml root@${K8MASTER}:${DEPLOY_DIR}
  if [ "$ENV_NAME"x == "prod"x ]; then

    ##ssh root@${K8MASTER} "$KUBECTL_PROD delete cm ${DEPLOY_CM} -n ${NS}"
    ##ssh root@${K8MASTER} "$KUBECTL_PROD create cm ${DEPLOY_CM} --from-file=${DEPLOY_DIR}/pym-ict-config -n ${NS}"
    ##ssh root@${K8MASTER} "$KUBECTL_PROD apply -f ${DEPLOY_DIR}/pym-nginx.yaml"
    ##ssh root@${K8MASTER} "$KUBECTL_PROD rollout restart deploy $CTROL_NAME -n ${NS}"
    
    ssh root@${K8MASTER} "$KUBECTL_PROD delete cm ${DEPLOY_CM} -n ${NS};$KUBECTL_PROD delete -f ${DEPLOY_DIR}/pym-ict-nginx.yaml"
    ssh root@${K8MASTER} "$KUBECTL_PROD create cm ${DEPLOY_CM} --from-file=${DEPLOY_DIR}/pym-web-config -n ${NS}"
    ssh root@${K8MASTER} "$KUBECTL_PROD apply -f ${DEPLOY_DIR}/pym-ict-nginx.yaml"

 else
    
    ##ssh root@${K8MASTER} "$KUBECTL delete cm ${DEPLOY_CM} -n ${NS}"
    ##ssh root@${K8MASTER} "$KUBECTL create cm ${DEPLOY_CM} --from-file=${DEPLOY_DIR}/pym-ict-config -n ${NS}"
    ##ssh root@${K8MASTER} "$KUBECTL apply -f ${DEPLOY_DIR}/pym-nginx.yaml"
    ##ssh root@${K8MASTER} "$KUBECTL rollout restart deploy $CTROL_NAME -n ${NS}"
    
    ssh root@${K8MASTER} "$KUBECTL delete cm ${DEPLOY_CM} -n ${NS};$KUBECTL delete -f ${DEPLOY_DIR}/pym-ict-nginx.yaml"
    ssh root@${K8MASTER} "$KUBECTL create cm ${DEPLOY_CM} --from-file=${DEPLOY_DIR}/pym-web-config -n ${NS}"
    ssh root@${K8MASTER} "$KUBECTL apply -f ${DEPLOY_DIR}/pym-ict-nginx.yaml"

 fi
 }



deploy(){
    build_icp_web
    cp_icp_web
    build_image
}

deploy
