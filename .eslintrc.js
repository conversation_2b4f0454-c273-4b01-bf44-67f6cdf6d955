module.exports = {
	root: true,
	parser: 'vue-eslint-parser',
	parserOptions: {
		parser: 'babel-eslint',
		sourceType: 'module'
	},
	env: {
		browser: true,
		node: true,
		es6: true
	},
	plugins: ['prettier'],
	extends: ['plugin:prettier/recommended', 'eslint:recommended'],
	rules: {
		'prettier/prettier': 'error',
		'generator-star-spacing': 'off',
		'no-unused-vars': 'off',
		'no-tabs': ['error', { allowIndentationTabs: true }],
		indent: ['error', 'tab']
	}
}
