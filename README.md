# pym-ict-v2

## Project setup
```
npm install -d --force
```

### Compiles and hot-reloads for development --VUE
```
npm run dev:web
```

### Compiles and minifies for production --VUE
```
npm run version
npm run build:web
```

### Compiles and hot-reloads for development --ELECTRON
```
npm run dev:win
npm run dev:linux-amd64
npm run dev:linux-arm64
```

### Compiles and minifies for production --ELECTRON
```
npm run build:win-exe
npm run build:linux-amd64-deb
npm run build:linux-arm64-deb
```

### 如果更新代码下来执行npm run electron:serve报code: 'ERR_ELECTRON_BUILDER_CANNOT_EXECUTE' ，需另见文档配置内容
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
