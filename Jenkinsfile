pipeline {
  agent any
	options{
		timestamps() // 日志时间
	}
	environment {
		SOURCE_DIR = '/opt/jenkins/jenkins_home/workspace' // jenkins工作目录
		def sshServer = ''
		def git_version = ''
		def master_host = '***********'
  }
  parameters {
        choice(name: 'deploy_env', choices:['dev', 'test', 'prod'], description: '请选择部署环境')
        string(name: 'git_fetch', defaultValue: 'V5.0.3.3', description: '准备构建的git版本,不填默认发布dev分支')
  }
  stages {
	stage('连接服务器') {
      steps {
				timeout(time:5, unit:"MINUTES"){
					script{
						sshServer = getServer("***********")
						// 初始化发布分支
						if(params.git_fetch == ''){
							git_version = "V5.0.3.3"
						}else {
							git_version = params.git_fetch
						}
						if(params.deploy_env == 'prod') {
						    master_host = '**********'
						} else if (params.deploy_env == 'test') {
						    master_host = '**************'
						}
					}
				}
      }
    }
    stage('拉取代码') {
      steps {
        timeout(time:5, unit:"MINUTES"){
          script{
            checkout([$class: 'GitSCM', branches: [[name: git_version]], extensions: [], userRemoteConfigs: [[credentialsId: '43bb7964-6eba-4f2b-90b7-7ad810c644e4', url: 'https://***************/pd-pym/code/pym-ict-v2.git']]])
          }
        }
      }
    }
	stage('打包构建镜像') {
      steps {
				script{
					sshCommand remote: sshServer, command: "source /etc/profile && sh -x ${SOURCE_DIR}/${JOB_NAME}/deploy/deploy.sh ${deploy_env} ${master_host} ${SOURCE_DIR}/${JOB_NAME}"
				}
      }
    }
  }

	post{
		success{
			script{
				currentBuild.description = "\n 构建成功"


			}
		}
		failure{
			script{
				currentBuild.description = "\n 构建失败"
			}
		}
		aborted{
			script{
				currentBuild.description = "\n 构建取消"
			}
		}
	}
}

// 声明一个获取远程服务的方法
def getServer(ip){
	def remote = [:]
	remote.name = "${ip}"
	remote.host = ip
	remote.port = 22
	remote.allowAnyHosts = true
	withCredentials([usernamePassword(credentialsId: '***********', passwordVariable: 'password', usernameVariable: 'user_name')]) {
		remote.user = "${user_name}"
		remote.password = "${password}"
	}
	return remote
}
