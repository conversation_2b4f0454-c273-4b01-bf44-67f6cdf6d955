【特别注意】
因为使用wsl，所以要分别在linux和win的环境下各装一次package.json中的依赖，为了避免重复删除和安装。
每次切换环境时，记得更换node_modules名称。
如在windows环境下将linux的node_modules改名为node_modules_linux
如在linux环境下将windows的node_modules改名为node_modules_win
同时把当前环境下的node_modules名称还原即可。

Windows
1、nodeJS 16.20.2 32-bit
2、python 3.11.9
3、visual studio 2022 community 【add desktop c++】
4、使用cnpm安装依赖 [因为electron或github访问不稳定]
    npm i -g cnpm
    cnpm i
    注意，凡是用cnpm安装后，再添加其他依赖务必同样使用cnpm


WSL【一定要注意glibc版本，低级兼容高级，但高级势必不兼容低级 储物柜的系统版本为2.28】
一定要安装Ubuntu 18.04版或其他glibc版本低于或等于2.28版本的操作系统
查看 glibc 版本命令 ldd --version
安装教程自行查找。

以下为安装环境的步骤
一、下载通用依赖
sudo apt update
sudo apt install wget build-essential zlib1g-dev \
libncurses5-dev libgdbm-dev libnss3-dev \
libssl-dev libreadline-dev libffi-dev \
libsqlite3-dev libbz2-dev

二、下载nvm【使用nvm安装，注意提示语。】
wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash

export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

三、使用nvm安装 nodeJS 16.20.2
nvm install 16.20.2

四、安装 python 3.11.9 [无法直接下载，需要执行以下命令]
    1、创建一个临时文件夹并进入
    mkdir /tmp
    cd /tmp

    2、下载 Python 3.11.9 解压后进入解压后的文件夹
    wget https://www.python.org/ftp/python/3.11.9/Python-3.11.9.tgz
    tar -xf Python-3.11.9.tgz
    cd Python-3.11.9

    3、编译源代码
    ./configure --enable-optimizations
    make -j 4
    sudo make altinstall

    4、验证安装
    python3.11 --version
    如果输出 Python 3.11.9 就对了

五、安装其他编译必须文件
sudo apt install gcc g++ make g++-multilib

六、指定环境变量
在项目根目录下 .npmrc 根据自身环境变量增加
python=/usr/local/bin/python3.11 【示例】

七、安装前端项目
npm i 或 cnpm i

八、执行相关打包命令

因为在linux环境下打包时，需要明确文件的权限，而通过WSL代理的文件需要二次添加权限

添加权限步骤
1、sudo nano /etc/wsl.conf

2、写入
[automount]
# 将 options 替换为你想要的挂载参数
options = "metadata,uid=1000,gid=1000,umask=22,fmask=111"

3、写入后关闭wsl再打开
wsl --shutdown
wsl -d Ubuntu-18.04

4、在wsl模式下进入项目目录
cd /mnt/d/code/rs-amt-web  [示例]

5、执行以下命令赋予权限
chmod -R +x node_modules/.bin/
chmod -R +x node_modules/.store/

6、执行打包命令
npm run build:linux