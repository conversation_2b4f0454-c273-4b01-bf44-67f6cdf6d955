@startuml
'https://plantuml.com/sequence-diagram

autonumber

Electron  -> 人脸识别SDK: GoNc_FaceRecognition_Init 人脸初始化
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_Create 人脸识别器创建
实战平台  -> Electron: 下发图片通过 url 转成 bgr 格式
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_GetFaceFeature人脸特征提取
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_AddPerson 新增人员
Electron  -> Electron: 点击人脸登录
人脸识别SDK  -> Electron: 返回抓拍图片
Electron  -> Electron: 转成 bgr 格式
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_GetFaceFeature 获取特征值
Electron  -> 人脸识别SDK: GoNc_FaceRecognition_SearchPerson 通过特征值搜索人员
Electron -> 实战平台: 通过特征值搜索获取到的人员信息,并登录

@endumlthentication Request
