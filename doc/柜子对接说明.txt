【注意注意注意注意注意注意注意注意】
【在li_dev_John分支下 打包一定是 npm run build:linux】

账号信息
xshell 登录服务
************ ecsuu/admin@123

ftp 登录服务
************:22 ecsuu/admin@123

ftp上传完后正对安装包 右键设置权限777

柜子包安装命令：【用xshell连接到副物柜服务】
sudo dpkg -i  xxxx    比如： pym-client_amd64_5.0.3.4.2025.07.15.1954.deb

卸载:
sudo dpkg -r pym-client

运行web端：
npm run dev:web
运行客户端：
npm run dev:win

日志：/opt/gosuncn/pym-client/log


【以下内容使用WSL可忽略】
解压 覆盖 unzip -o xxx

下载向日葵远程电脑：【使用WSL可忽略】
137 718 378 5  A12345   电脑密码：python
使用xshell  访问服务：
**************   / 123456

代码在 /home/<USER>/Desktop
打包命令：
npm run update-version && npm run build:web && npm run build:linux-amd64-deb
打包完成后 在/home/<USER>/Desktop/code/x64_linux文件夹下
拷出打包文件安装在桌面柜子