## 运行环境：
nodejs 16.20.2
npm 8.19.4

## 依赖安装：
```
npm install
```

## 本地启动命令：
```
1、启动前端vue工程：npm run dev:win

本地windows环境：
    2、启动electron工程：npm run dev:win
本地linux环境：
    2、启动electron工程：npm run dev:linux-amd64
    3、启动electron工程：npm run dev:linux-arm64
```
## 编译打包命令：
```
1、linux环境：npm run build:linux
```
## 工程目录结构：
```
├── client：electron工程源码
    ├── dll：electron工程对接dll封装 - 人脸识别算法、虚拟键盘
    ├── ipc：封装electron进程与渲染进程间通信
    ├── light：硬件对接补光灯
    ├── lock：硬件对接锁控插件：DC、KA、JJ
    ├── log：终端本地日志
    ├── mqtt：对接mqtt服务（硬件云平台）
    ├── server：终端开启的服务：ws: 2400 远程开柜服务、http: 5888 远程开柜服务、9527 远程运维服务  //改成了9527
    ├── upgrad：electron工程升级逻辑
├── extraResources：无需编译的终端文件
├── main.js：electron工程主进程入口文件
├── public：前端vue工程配置文件 normal_mode
├── src：前端vue工程源码
    ├── api：前端vue工程api封装
    ├── assets：前端vue工程静态资源
    ├── components：前端vue工程组件
    ├── router：前端vue工程路由文件
    ├── directives：前端vue工程自定义指令
    ├── libs：前端vue工程工具类、硬件对接
    ├── stroe：前端vue工程状态管理
    ├── views：前端vue工程页面
        ├── login：人脸、账号登录组件
        ├── normal_mode：保管员存、取物品业务代码
        ├── temporary_mode：暂存业务代码
    ├── App.vue：前端vue工程入口文件
    ├── web：终端运维工程入口文件
    ├── main：前端vue工程主进程入口文件
├── auth_window：授权逻辑文件
├── build-terminal-version：打包版本生成文件
├── x64_linux：linux环境编译模板脚本
├── package.json：工程依赖配置文件
├── vue.config.js：vue工程配置文件
