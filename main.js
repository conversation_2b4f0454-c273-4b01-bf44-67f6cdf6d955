'use strict'
// Electron 主程序入口文件
const { app, protocol, BrowserWindow, ipcMain, shell, Menu, Tray, globalShortcut } = require('electron')
const path = require('path')
const fs = require('fs')
// const { exec } = require('child_process')
const { initDll, unitDll } = require('./client/dll/api')
const { initIpc } = require('./client/ipc/index')
const authWindow = require('./auth_window')
const operationCenterServer = require('./client/server/operation_center')
const tools = require('./client/mqtt/tools')
const { actualVersion } = require('./package.json')
const Logger = require('./client/log/index')
const log = new Logger()

// 托盘对象
let appTray = null
let mainWindow = null
let basePath = process.cwd()
let extraResources = path.join(basePath, 'extraResources')
let appIcon = path.join(basePath, process.platform == 'linux' ? 'public/icon.png' : 'public/icon.ico')

if (app.isPackaged) {
	basePath = path.dirname(app.getPath('exe'))
	extraResources = path.join(process.resourcesPath, 'extraResources')
	appIcon = path.join(process.resourcesPath, process.platform == 'linux' ? 'icon.png' : 'icon.ico')
	// 开机自启动
	const exeName = path.basename(process.execPath)
	app.setLoginItemSettings({
		openAtLogin: true,
		openAsHidden: false,
		path: process.execPath,
		args: ['--processStart', `"${exeName}"`]
	})
} else {
	if (process.platform === 'win32') {
		process.on('message', (data) => {
			if (data === 'graceful-exit') {
				app.quit()
			}
		})
	} else {
		process.on('SIGTERM', () => {
			app.quit()
		})
	}
}

// 获取本地json文件的路径
const newFile_path = path.join(extraResources, 'serverConfig.json')
const result = JSON.parse(fs.readFileSync(newFile_path))
if (!result.productName) {
	result.productName = '附物管理终端'
	try {
		const { ip } = tools.getIpMAC()
		result.productName += `(${ip})`
		fs.writeFileSync(newFile_path, JSON.stringify(result, null, 2))
	} catch (error) {
		log.info('获取MAC地址异常:', error)
	}
}
global.basePath = basePath
global.pymClientDataPath = path.join(basePath, '/Log')
global.terminalConfigFilePath = newFile_path
global.upgradeStatus = false

// 关闭启动页窗口
ipcMain.on('close-loader', () => {
	Object.assign(result, JSON.parse(fs.readFileSync(newFile_path)))
	// 处理linux 下全屏问题
	mainWindow.setFullScreen(false)
	mainWindow.setFullScreen(true)
	// 关闭键盘进程
	// if (process.platform == 'linux') {
	// exec('kill -9 $(pgrep onboard)')
	// }
	mainWindow.show()
	authWindow.closeWin()
})

ipcMain.on('openDevTool', () => {
	mainWindow?.webContents.openDevTools()
})

// 创建应用主窗口
function createWindow() {
	mainWindow = new BrowserWindow({
		title: '附物管理终端',
		icon: appIcon,
		width: result.winWidth,
		height: result.winHeight,
		// frame: result.isFrame, // 去掉顶部导航 去掉关闭按钮 最大化最小化按钮
		fullscreen: result.fullscreen, // 是否全屏
		transparent: result.transparent, // 窗口颜色
		alwaysOnTop: result.alwaysOnTop, // 应用是否浮动，可改变层级
		resizable: result.resizable,
		sandbox: true, // 沙盒选项,这个很重要
		show: false, // 页面是否在创建时显示,开启启动页需要false
		webPreferences: {
			devTools: true,
			nodeIntegration: true, // 是否集成 Nodejs,把之前预加载的js去了，发现也可以运行
			contextIsolation: true,
			webSecurity: false, // 允许跨域
			preload: path.join(extraResources, 'preload.js'),
			webgl: false
		}
	})
	setSystemTray()
	initDll(['face', 'osk'])
	if (result.setMenu) {
		// mainWindow.setMenu(null) // 隐藏顶部操作菜单，不包含最大化，最小化，关闭按钮
	}
	result.alwaysOnTop && mainWindow.setAlwaysOnTop(true, 'pop-up-menu') // 窗口悬浮置于windows系统栏之上
	mainWindow.hide()
	const loadURL = app.isPackaged ? `file://${__dirname}/dist/index.html#/home` : 'http://localhost:8840/#/home'
	mainWindow.loadURL(loadURL)
	// 是否开启调试模式
	if (!app.isPackaged || result.openDev) {
		mainWindow.webContents.openDevTools()
	}
	globalShortcut.register('CommandOrControl+Shift+i', () => {
		mainWindow.webContents.toggleDevTools()
	})
	initIpc(mainWindow, result)
	// 程序运行一天检查一次
	const logInterval = setInterval(() => {
		log.deleteLog(result.logSaveDay)
	}, 24 * 60 * 60 * 1000)
	mainWindow.on('closed', () => {
		unitDll(['face'])
		mainWindow = null
		clearInterval(logInterval)
		app.exit()
	})
	mainWindow.on('close', (_event) => {
		unitDll(['face'])
		mainWindow.webContents.send('isClose', true)
		app.exit()
	})
	// 删除日志
	log.deleteLog(result.logSaveDay)
	global.mainWin = mainWindow
}
// 系统托盘加图标、右键菜单
function setSystemTray() {
	// 系统托盘右键菜单
	const trayMenuTemplate = [
		{
			label: '版本号：' + actualVersion,
			click: () => {
				console.log('---')
			}
		},
		{
			label: '配置',
			click: () => {
				if (process.platform == 'win32') {
					shell.openExternal(newFile_path)
				}
				if (process.platform == 'linux') {
					shell.openPath(newFile_path)
				}
			}
		},
		{
			label: '退出',
			click: () => {
				app.exit()
			}
		}
	]

	// 系统托盘图标目录
	appTray = new Tray(appIcon)
	// 图标的上下文菜单
	const contextMenu = Menu.buildFromTemplate(trayMenuTemplate)
	// 设置此托盘图标的悬停提示内容
	appTray.setToolTip(result.terminal || '附物管理终端')
	// 设置此图标的上下文菜单
	appTray.setContextMenu(contextMenu)
}

// 添加远程页面关闭应用功能
ipcMain.on('close', (event, arg) => {
	app.quit()
})
// 传输配置到渲染进程
ipcMain.on('getTermConfig', (event, arg) => {
	Object.assign(result, JSON.parse(fs.readFileSync(newFile_path)))
	event.returnValue = result
})
// 日志调试
ipcMain.on('logger', (event, data) => {
	const ret = JSON.parse(data)
	if (ret.length) {
		ret.length === 1 ? log.info(ret[0]) : log.info(ret[0], ret[1])
	}
})
ipcMain.on('setTermConfig', (event, arg) => {
	Object.assign(result, arg)
	log.info('客户端配置存储开始:', result)
	fs.writeFileSync(newFile_path, JSON.stringify(result, null, 2))
})
ipcMain.on('get-mac-address', (event, macAddress) => {
	event.returnValue = tools.getMacAddress()
})
ipcMain.on('is-main-window', (event) => {
	event.returnValue = mainWindow != null
})

// 实例检测
const gotTheLock = app.requestSingleInstanceLock()
if (!gotTheLock) {
	app.quit()
} else {
	app.on('second-instance', (event, commandLine, workingDirectory) => {
		if (mainWindow) {
			if (mainWindow.isMinimized()) mainWindow.restore()
			mainWindow.focus()
		}
	})
	app.on('window-all-closed', () => {
		// // 开启键盘进程
		// if (process.platform == 'linux') {
		// exec('dbus-send --type=method_call --dest=org.onboard.Onboard /org/onboard/Onboard/Keyboard org.onboard.Onboard.Keyboard.Show')
		// }
		if (process.platform !== 'darwin') {
			app.quit()
		}
	})
	app.on('activate', () => {
		if (BrowserWindow.getAllWindows().length === 0) createWindow()
	})
	app.on('ready', async () => {
		const bool = await authWindow.createWin(extraResources, appIcon)
		if (bool) {
			createWindow()
			operationCenterServer.init()
		}
	})
}

// 为协议注册特权---可访问摄像头
protocol.registerSchemesAsPrivileged([
	{
		scheme: 'http',
		privileges: {
			bypassCSP: true,
			secure: true,
			supportFetchAPI: true,
			corsEnabled: true
		}
	}
])

app.allowRendererProcessReuse = true
app.enableRemoteModule = true

// 禁用当前应用程序的硬件加速
app.disableHardwareAcceleration()
